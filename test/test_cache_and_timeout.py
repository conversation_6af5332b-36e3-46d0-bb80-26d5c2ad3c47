#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试缓存机制和超时时间的修复
"""

import sys
import time
sys.path.append('/Users/<USER>/ai-service')

from common.config.code_review_config import CodeReviewConfig, DynamicModelConfig
from common.services.llm_service import LLMService


def test_cache_mechanism():
    """测试缓存机制"""
    print("=== 测试缓存机制 ===")
    
    try:
        # 清除所有缓存
        DynamicModelConfig.clear_all_cache()
        print("✅ 清除缓存成功")
        
        # 创建多个配置实例
        config1 = DynamicModelConfig()
        config2 = DynamicModelConfig()
        
        print("创建了两个DynamicModelConfig实例")
        
        # 第一次获取配置（应该从数据库读取）
        print("\n--- 第一次获取配置 ---")
        start_time = time.time()
        review_config1 = config1.get_review_model_config()
        duration1 = time.time() - start_time
        print(f"实例1获取配置耗时: {duration1:.3f}秒")
        print(f"获取到模型: {review_config1['model_name']}")
        
        # 第二次获取配置（应该从缓存读取）
        print("\n--- 第二次获取配置（同一实例） ---")
        start_time = time.time()
        review_config1_cached = config1.get_review_model_config()
        duration2 = time.time() - start_time
        print(f"实例1再次获取配置耗时: {duration2:.3f}秒")
        print(f"配置是否相同: {review_config1 == review_config1_cached}")
        
        # 不同实例获取配置（测试类级别缓存）
        print("\n--- 不同实例获取配置 ---")
        start_time = time.time()
        review_config2 = config2.get_review_model_config()
        duration3 = time.time() - start_time
        print(f"实例2获取配置耗时: {duration3:.3f}秒")
        print(f"两个实例配置是否相同: {review_config1 == review_config2}")
        
        # 验证缓存效果
        print(f"\n--- 缓存效果分析 ---")
        print(f"首次获取耗时: {duration1:.3f}秒")
        print(f"缓存获取耗时: {duration2:.3f}秒")
        print(f"跨实例获取耗时: {duration3:.3f}秒")
        
        if duration2 < duration1 and duration3 < duration1:
            print("✅ 缓存机制工作正常")
        else:
            print("⚠️  缓存机制可能存在问题")
        
        # 测试缓存清除
        print("\n--- 测试缓存清除 ---")
        DynamicModelConfig.clear_all_cache()
        start_time = time.time()
        review_config_after_clear = config1.get_review_model_config()
        duration4 = time.time() - start_time
        print(f"清除缓存后获取配置耗时: {duration4:.3f}秒")
        
        if duration4 > duration2:
            print("✅ 缓存清除功能正常")
        else:
            print("⚠️  缓存清除可能存在问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存机制测试失败: {e}")
        return False


def test_timeout_configuration():
    """测试超时时间配置"""
    print("\n=== 测试超时时间配置 ===")
    
    try:
        # 获取配置
        review_config = CodeReviewConfig.get_review_model_config()
        format_config = CodeReviewConfig.get_format_model_config()
        
        print(f"审查模型超时配置: {review_config.get('timeout_seconds', '未设置')}秒")
        print(f"格式化模型超时配置: {format_config.get('timeout_seconds', '未设置')}秒")
        
        # 创建LLM服务
        llm_service = LLMService()
        
        # 测试客户端创建（不实际调用）
        print("\n--- 测试客户端创建 ---")
        try:
            review_client = llm_service.get_review_client()
            format_client = llm_service.get_format_client()
            
            print("✅ 审查客户端创建成功")
            print("✅ 格式化客户端创建成功")
            
            # 检查客户端是否包含超时配置
            # 注意：这里我们无法直接访问客户端的超时配置，但可以通过日志确认
            print("超时配置已应用到客户端（请查看日志确认）")
            
            return True
            
        except Exception as client_error:
            print(f"❌ 客户端创建失败: {client_error}")
            return False
        
    except Exception as e:
        print(f"❌ 超时配置测试失败: {e}")
        return False


def test_cache_expiration():
    """测试缓存过期机制"""
    print("\n=== 测试缓存过期机制 ===")
    
    try:
        # 清除缓存
        DynamicModelConfig.clear_all_cache()
        
        # 临时修改缓存持续时间为2秒（仅用于测试）
        original_duration = DynamicModelConfig._cache_duration
        DynamicModelConfig._cache_duration = 2
        
        config = DynamicModelConfig()
        
        # 第一次获取配置
        print("第一次获取配置...")
        config1 = config.get_review_model_config()
        print(f"获取到模型: {config1['model_name']}")
        
        # 立即再次获取（应该从缓存）
        print("立即再次获取配置...")
        start_time = time.time()
        config2 = config.get_review_model_config()
        duration1 = time.time() - start_time
        print(f"缓存获取耗时: {duration1:.3f}秒")
        
        # 等待缓存过期
        print("等待3秒让缓存过期...")
        time.sleep(3)
        
        # 缓存过期后再次获取
        print("缓存过期后获取配置...")
        start_time = time.time()
        config3 = config.get_review_model_config()
        duration2 = time.time() - start_time
        print(f"过期后获取耗时: {duration2:.3f}秒")
        
        # 恢复原始缓存持续时间
        DynamicModelConfig._cache_duration = original_duration
        
        if duration2 > duration1:
            print("✅ 缓存过期机制工作正常")
            return True
        else:
            print("⚠️  缓存过期机制可能存在问题")
            return False
        
    except Exception as e:
        print(f"❌ 缓存过期测试失败: {e}")
        # 确保恢复原始设置
        DynamicModelConfig._cache_duration = original_duration
        return False


def test_global_cache_sharing():
    """测试全局缓存共享"""
    print("\n=== 测试全局缓存共享 ===")
    
    try:
        # 清除缓存
        DynamicModelConfig.clear_all_cache()
        
        # 创建多个实例
        instances = [DynamicModelConfig() for _ in range(3)]
        
        # 第一个实例获取配置
        print("实例1获取配置...")
        start_time = time.time()
        config1 = instances[0].get_review_model_config()
        duration1 = time.time() - start_time
        print(f"实例1获取耗时: {duration1:.3f}秒")
        
        # 其他实例获取配置（应该从共享缓存获取）
        durations = []
        for i, instance in enumerate(instances[1:], 2):
            start_time = time.time()
            config = instance.get_review_model_config()
            duration = time.time() - start_time
            durations.append(duration)
            print(f"实例{i}获取耗时: {duration:.3f}秒")
            print(f"实例{i}配置与实例1相同: {config == config1}")
        
        # 验证缓存共享效果
        avg_cached_duration = sum(durations) / len(durations)
        print(f"\n平均缓存获取耗时: {avg_cached_duration:.3f}秒")
        
        if all(d < duration1 for d in durations):
            print("✅ 全局缓存共享工作正常")
            return True
        else:
            print("⚠️  全局缓存共享可能存在问题")
            return False
        
    except Exception as e:
        print(f"❌ 全局缓存共享测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试缓存机制和超时时间修复...")
    
    test_results = []
    
    # 1. 测试缓存机制
    result = test_cache_mechanism()
    test_results.append(("缓存机制", result))
    
    # 2. 测试超时配置
    result = test_timeout_configuration()
    test_results.append(("超时配置", result))
    
    # 3. 测试缓存过期
    result = test_cache_expiration()
    test_results.append(("缓存过期", result))
    
    # 4. 测试全局缓存共享
    result = test_global_cache_sharing()
    test_results.append(("全局缓存共享", result))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有修复验证通过！")
    else:
        print("⚠️  部分修复需要进一步检查")


if __name__ == "__main__":
    main()
