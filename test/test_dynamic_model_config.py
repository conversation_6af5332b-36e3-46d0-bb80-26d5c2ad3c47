#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态模型配置功能
"""

import sys
import json
sys.path.append('/Users/<USER>/ai-service')

from common.config.code_review_config import CodeReviewConfig, DynamicModelConfig
from common.dao.llm_model_config_dao import LLMModelConfigDAO, ModelType, ModelStatus
from common.services.model_config_service import ModelConfigService
from common.factory.service_factory import get_llm_service, refresh_llm_service


def test_database_table_exists():
    """测试数据库表是否存在"""
    try:
        dao = LLMModelConfigDAO()
        # 尝试查询表结构
        from common.utils.single_db import get_dbhandler
        db = get_dbhandler()
        result = db.query("SHOW TABLES LIKE 'llm_model_config'")
        
        if result:
            print("✅ llm_model_config表存在")
            
            # 检查表结构
            columns = db.query("DESCRIBE llm_model_config")
            print(f"表结构包含 {len(columns)} 个字段")
            return True
        else:
            print("❌ llm_model_config表不存在")
            print("请先执行SQL脚本: resource/sql/llm_model_config.sql")
            return False
            
    except Exception as e:
        print(f"❌ 检查数据库表失败: {e}")
        return False


def test_dao_operations():
    """测试DAO操作"""
    try:
        dao = LLMModelConfigDAO()
        
        # 测试获取审查模型
        print("\n=== 测试获取审查模型 ===")
        review_models = dao.get_active_models_by_type(ModelType.REVIEW)
        print(f"找到 {len(review_models)} 个审查模型")
        
        if review_models:
            primary_model = review_models[0]
            print(f"主要审查模型: {primary_model.model_name}")
            print(f"优先级: {primary_model.priority}")
            print(f"状态: {primary_model.status}")
        
        # 测试获取格式化模型
        print("\n=== 测试获取格式化模型 ===")
        format_models = dao.get_active_models_by_type(ModelType.FORMAT)
        print(f"找到 {len(format_models)} 个格式化模型")
        
        if format_models:
            primary_model = format_models[0]
            print(f"主要格式化模型: {primary_model.model_name}")
            print(f"优先级: {primary_model.priority}")
            print(f"状态: {primary_model.status}")
        
        return len(review_models) > 0 and len(format_models) > 0
        
    except Exception as e:
        print(f"❌ DAO操作测试失败: {e}")
        return False


def test_dynamic_config():
    """测试动态配置"""
    try:
        print("\n=== 测试动态配置 ===")
        
        # 测试获取审查模型配置
        review_config = CodeReviewConfig.get_review_model_config()
        print(f"审查模型配置: {review_config['model_name']}")
        print(f"API地址: {review_config['api_url']}")
        print(f"最大tokens: {review_config['max_tokens']}")
        
        # 测试获取格式化模型配置
        format_config = CodeReviewConfig.get_format_model_config()
        print(f"格式化模型配置: {format_config['model_name']}")
        print(f"API地址: {format_config['api_url']}")
        print(f"最大tokens: {format_config['max_tokens']}")
        
        # 测试缓存机制
        print("\n=== 测试缓存机制 ===")
        config1 = CodeReviewConfig.get_review_model_config()
        config2 = CodeReviewConfig.get_review_model_config()
        print(f"缓存测试: {config1 == config2}")
        
        # 测试清除缓存
        CodeReviewConfig.clear_model_cache()
        config3 = CodeReviewConfig.get_review_model_config()
        print("缓存已清除，重新获取配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 动态配置测试失败: {e}")
        return False


def test_llm_service_integration():
    """测试LLM服务集成"""
    try:
        print("\n=== 测试LLM服务集成 ===")
        
        llm_service = get_llm_service()
        
        # 测试获取客户端（不实际调用）
        review_client = llm_service.get_review_client()
        format_client = llm_service.get_format_client()
        
        print(f"审查客户端创建成功: {review_client is not None}")
        print(f"格式化客户端创建成功: {format_client is not None}")
        
        # 测试刷新配置
        print("测试配置刷新...")
        refresh_llm_service()
        print("配置刷新完成")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM服务集成测试失败: {e}")
        return False


def test_model_config_service():
    """测试模型配置服务"""
    try:
        print("\n=== 测试模型配置服务 ===")
        
        service = ModelConfigService()
        
        # 测试获取所有模型
        all_models = service.get_all_models()
        print(f"所有模型数量: {len(all_models)}")
        
        # 测试按类型获取模型
        review_models = service.get_models_by_type('REVIEW')
        format_models = service.get_models_by_type('FORMAT')
        
        print(f"审查模型数量: {len(review_models)}")
        print(f"格式化模型数量: {len(format_models)}")
        
        # 测试获取主要模型
        primary_review = service.get_primary_model('REVIEW')
        primary_format = service.get_primary_model('FORMAT')
        
        if primary_review:
            print(f"主要审查模型: {primary_review['model_name']}")
        if primary_format:
            print(f"主要格式化模型: {primary_format['model_name']}")
        
        # 测试获取模型统计
        if all_models:
            model_id = all_models[0]['id']
            stats = service.get_model_stats(model_id)
            if stats:
                print(f"模型统计 - 成功率: {stats['success_rate']:.2f}%")
                print(f"总调用次数: {stats['total_calls']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型配置服务测试失败: {e}")
        return False


def test_failover_simulation():
    """测试故障转移模拟"""
    try:
        print("\n=== 测试故障转移模拟 ===")
        
        # 这里只是模拟测试，不实际调用LLM
        dao = LLMModelConfigDAO()
        
        # 获取所有审查模型
        review_models = dao.get_active_models_by_type(ModelType.REVIEW)
        
        if len(review_models) > 1:
            print(f"发现 {len(review_models)} 个审查模型，支持故障转移")
            for i, model in enumerate(review_models):
                print(f"  {i+1}. {model.model_name} (优先级: {model.priority})")
        else:
            print("只有1个审查模型，建议添加备用模型以支持故障转移")
        
        # 获取所有格式化模型
        format_models = dao.get_active_models_by_type(ModelType.FORMAT)
        
        if len(format_models) > 1:
            print(f"发现 {len(format_models)} 个格式化模型，支持故障转移")
            for i, model in enumerate(format_models):
                print(f"  {i+1}. {model.model_name} (优先级: {model.priority})")
        else:
            print("只有1个格式化模型，建议添加备用模型以支持故障转移")
        
        return True
        
    except Exception as e:
        print(f"❌ 故障转移测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试动态模型配置功能...")
    
    test_results = []
    
    # 1. 测试数据库表
    print("\n" + "="*50)
    print("1. 测试数据库表")
    print("="*50)
    result = test_database_table_exists()
    test_results.append(("数据库表检查", result))
    
    if not result:
        print("⚠️  数据库表不存在，跳过后续测试")
        return
    
    # 2. 测试DAO操作
    print("\n" + "="*50)
    print("2. 测试DAO操作")
    print("="*50)
    result = test_dao_operations()
    test_results.append(("DAO操作", result))
    
    # 3. 测试动态配置
    print("\n" + "="*50)
    print("3. 测试动态配置")
    print("="*50)
    result = test_dynamic_config()
    test_results.append(("动态配置", result))
    
    # 4. 测试LLM服务集成
    print("\n" + "="*50)
    print("4. 测试LLM服务集成")
    print("="*50)
    result = test_llm_service_integration()
    test_results.append(("LLM服务集成", result))
    
    # 5. 测试模型配置服务
    print("\n" + "="*50)
    print("5. 测试模型配置服务")
    print("="*50)
    result = test_model_config_service()
    test_results.append(("模型配置服务", result))
    
    # 6. 测试故障转移
    print("\n" + "="*50)
    print("6. 测试故障转移")
    print("="*50)
    result = test_failover_simulation()
    test_results.append(("故障转移", result))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！动态模型配置功能正常")
    else:
        print("⚠️  部分测试失败，请检查相关功能")


if __name__ == "__main__":
    main()
