#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代码审查改造功能
"""

import sys
import json
sys.path.append('/Users/<USER>/ai-service')

from service.code_review import run_format_task
from common.utils.single_db import get_dbhandler

def test_database_connection():
    """测试数据库连接"""
    try:
        db = get_dbhandler()
        result = db.query("SELECT 1 as test")
        print(f"数据库连接测试成功: {result}")
        return True
    except Exception as e:
        print(f"数据库连接测试失败: {e}")
        return False

def test_table_exists():
    """测试code_review_ai_info表是否存在"""
    try:
        db = get_dbhandler()
        result = db.query("SHOW TABLES LIKE 'code_review_ai_info'")
        if result:
            print("code_review_ai_info表存在")
            return True
        else:
            print("code_review_ai_info表不存在")
            return False
    except Exception as e:
        print(f"检查表存在性失败: {e}")
        return False

def test_insert_test_record():
    """插入一条测试记录"""
    try:
        db = get_dbhandler()
        insert_sql = """
        INSERT INTO code_review_ai_info 
        (project_id, mr_iid, user_name, create_time, start_time, end_time, commit_sha, status, version, contents, params) 
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            9999,  # project_id
            9999,  # mr_iid
            'test_user',  # user_name
            '2025-01-01 08:00:00',  # create_time
            '2025-01-01 08:00:00',  # start_time
            '2025-01-01 08:00:00',  # end_time
            'test_commit_sha',  # commit_sha
            0,  # status
            1,  # version
            'test_contents',  # contents
            ''  # params (空字符串)
        )
        
        last_id = db._exec(insert_sql, params=params, is_query=False)
        print(f"插入测试记录成功，ID: {last_id}")
        return last_id
    except Exception as e:
        print(f"插入测试记录失败: {e}")
        return None

def test_update_params(record_id):
    """测试更新params字段"""
    try:
        # 模拟格式化结果
        test_formatted = {
            "issues": [
                {
                    "priority": "高",
                    "file": "test.py",
                    "line_range": "L1-L10",
                    "description": "测试问题描述",
                    "suggestion": "测试建议"
                }
            ],
            "summary": "测试总结",
            "score": 85
        }
        
        db = get_dbhandler()
        params_json = json.dumps(test_formatted, ensure_ascii=False)
        
        # 更新params字段
        update_sql = "UPDATE code_review_ai_info SET params = %s WHERE id = %s"
        db._exec(update_sql, params=(params_json, record_id), is_query=False)
        
        # 验证更新结果
        select_sql = f"SELECT params FROM code_review_ai_info WHERE id = {record_id}"
        result = db.query(select_sql)
        
        if result and result[0][0]:
            saved_data = json.loads(result[0][0])
            print(f"更新params字段成功，保存的数据: {saved_data}")
            return True
        else:
            print("更新params字段失败，未找到数据")
            return False
            
    except Exception as e:
        print(f"更新params字段失败: {e}")
        return False

def cleanup_test_record(record_id):
    """清理测试记录"""
    try:
        db = get_dbhandler()
        delete_sql = f"DELETE FROM code_review_ai_info WHERE id = {record_id}"
        db.modify(delete_sql)
        print(f"清理测试记录成功，ID: {record_id}")
    except Exception as e:
        print(f"清理测试记录失败: {e}")

def main():
    """主测试函数"""
    print("开始测试代码审查改造功能...")
    
    # 1. 测试数据库连接
    if not test_database_connection():
        print("数据库连接失败，终止测试")
        return
    
    # 2. 测试表是否存在
    if not test_table_exists():
        print("code_review_ai_info表不存在，终止测试")
        return
    
    # 3. 插入测试记录
    record_id = test_insert_test_record()
    if not record_id:
        print("插入测试记录失败，终止测试")
        return
    
    try:
        # 4. 测试更新params字段
        if test_update_params(record_id):
            print("✅ 所有测试通过！代码审查改造功能正常")
        else:
            print("❌ 更新params字段测试失败")
    finally:
        # 5. 清理测试记录
        cleanup_test_record(record_id)

if __name__ == "__main__":
    main()
