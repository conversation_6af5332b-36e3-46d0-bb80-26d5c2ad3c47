#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代码审查重构后的功能
"""

import sys
import json
sys.path.append('/Users/<USER>/ai-service')

from service.code_review import get_code_review_service, filter_files, format_llm_response
from common.factory.service_factory import get_code_review_dao
from common.config.code_review_config import CodeReviewConfig
from common.models.code_review_models import CodeReviewRequest, FileChange

def test_database_connection():
    """测试数据库连接"""
    try:
        dao = get_code_review_dao()
        # 通过DAO测试数据库连接
        result = dao.get_review_by_id(1)  # 尝试查询一个记录
        print(f"数据库连接测试成功")
        return True
    except Exception as e:
        print(f"数据库连接测试失败: {e}")
        return False

def test_config_loading():
    """测试配置加载"""
    try:
        config = CodeReviewConfig()
        file_types = config.get_supported_file_types()
        max_tokens = config.get_max_tokens()
        max_code_plus = config.get_max_code_plus()

        print(f"支持的文件类型数量: {len(file_types)}")
        print(f"最大token限制: {max_tokens}")
        print(f"最大代码行数限制: {max_code_plus}")

        return len(file_types) > 0 and max_tokens > 0 and max_code_plus > 0
    except Exception as e:
        print(f"配置加载测试失败: {e}")
        return False

def test_file_filtering():
    """测试文件过滤功能"""
    try:
        test_files = [
            "src/main.py",
            "docs/readme.md",
            "config/settings.yaml",
            "src/module.go",
            "src/module.java",
            "src/module.js",
            "src/module.pyc",
        ]

        filtered_files = filter_files(test_files)
        print(f"原始文件数: {len(test_files)}")
        print(f"过滤后文件数: {len(filtered_files)}")
        print(f"过滤后的文件: {filtered_files}")

        # 应该过滤掉 .md, .yaml, .pyc 文件
        expected_files = ["src/main.py", "src/module.go", "src/module.java", "src/module.js"]
        return len(filtered_files) == len(expected_files)
    except Exception as e:
        print(f"文件过滤测试失败: {e}")
        return False

def test_dao_operations():
    """测试DAO操作"""
    try:
        dao = get_code_review_dao()

        # 测试创建记录
        record_id = dao.create_review_record("9999", "9999", "processing")
        if not record_id:
            print("创建测试记录失败")
            return False

        print(f"创建测试记录成功，ID: {record_id}")

        # 测试更新记录
        test_formatted = {
            "issues": [
                {
                    "priority": "高",
                    "file": "test.py",
                    "lines": [{"start": 1, "end": 10}],
                    "description": "测试问题描述",
                    "suggestion": "测试建议",
                    "impact": "测试影响",
                    "example": "测试示例"
                }
            ]
        }

        success = dao.update_review_result(record_id, test_formatted)
        if not success:
            print("更新测试记录失败")
            return False

        print("DAO操作测试成功")
        return True

    except Exception as e:
        print(f"DAO操作测试失败: {e}")
        return False

def test_service_integration():
    """测试服务集成"""
    try:
        service = get_code_review_service()

        # 测试文件过滤
        test_files = ["test.py", "test.md", "test.go"]
        filtered = service.filter_files(test_files)
        print(f"服务文件过滤测试: {len(test_files)} -> {len(filtered)}")

        # 测试LLM响应格式化
        test_response = """
        ### 2. 问题识别与优化建议

        #### 1. **问题描述**: 测试问题
        - **文件**: test.py
        - **行号**: L1-L5
        - **影响**: 测试影响
        - **优化建议**: 测试建议
        - **修复优先级**: 高
        """

        formatted = service.format_llm_response(test_response)
        print(f"LLM响应格式化测试成功，问题数: {len(formatted.get('issues', []))}")

        return True

    except Exception as e:
        print(f"服务集成测试失败: {e}")
        return False

def test_model_validation():
    """测试模型验证"""
    try:
        # 测试FileChange模型
        file_change = FileChange(
            new_path="test.py",
            diff="@@ -1,3 +1,4 @@\n+# 新增注释\n def test():\n     pass"
        )
        print(f"FileChange模型测试: 新增行数 = {file_change.added_lines}")

        # 测试CodeReviewRequest模型
        request = CodeReviewRequest(
            project_id="123",
            mr_iid="456",
            changes=[file_change]
        )

        is_valid = request.validate()
        print(f"CodeReviewRequest模型验证: {is_valid}")

        return is_valid

    except Exception as e:
        print(f"模型验证测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试代码审查重构后的功能...")

    test_results = []

    # 1. 测试数据库连接
    print("\n=== 测试数据库连接 ===")
    result = test_database_connection()
    test_results.append(("数据库连接", result))

    # 2. 测试配置加载
    print("\n=== 测试配置加载 ===")
    result = test_config_loading()
    test_results.append(("配置加载", result))

    # 3. 测试文件过滤
    print("\n=== 测试文件过滤 ===")
    result = test_file_filtering()
    test_results.append(("文件过滤", result))

    # 4. 测试DAO操作
    print("\n=== 测试DAO操作 ===")
    result = test_dao_operations()
    test_results.append(("DAO操作", result))

    # 5. 测试服务集成
    print("\n=== 测试服务集成 ===")
    result = test_service_integration()
    test_results.append(("服务集成", result))

    # 6. 测试模型验证
    print("\n=== 测试模型验证 ===")
    result = test_model_validation()
    test_results.append(("模型验证", result))

    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1

    print("="*50)
    print(f"总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("🎉 所有测试通过！代码审查重构功能正常")
    else:
        print("⚠️  部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
