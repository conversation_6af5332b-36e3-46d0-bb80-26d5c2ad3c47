#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型配置管理工具
用于管理LLM模型配置，支持查看、更新、刷新等操作
"""

import sys
import argparse
sys.path.append('/Users/<USER>/ai-service')

from common.services.model_config_service import ModelConfigService
from common.dao.llm_model_config_dao import LLMModelConfigDAO
from common.factory.service_factory import refresh_llm_service


def list_models(model_type=None):
    """列出模型配置"""
    service = ModelConfigService()
    
    try:
        if model_type:
            models = service.get_models_by_type(model_type.upper())
            print(f"\n{model_type.upper()} 类型模型配置:")
        else:
            models = service.get_all_models()
            print("\n所有模型配置:")
        
        if not models:
            print("未找到模型配置")
            return
        
        print("-" * 100)
        print(f"{'ID':<5} {'模型名称':<30} {'类型':<8} {'状态':<10} {'优先级':<6} {'成功次数':<8} {'失败次数':<8}")
        print("-" * 100)
        
        for model in models:
            print(f"{model['id']:<5} {model['model_name']:<30} {model['model_type']:<8} "
                  f"{model['status']:<10} {model['priority']:<6} {model['success_count']:<8} {model['error_count']:<8}")
        
        print("-" * 100)
        print(f"总计: {len(models)} 个模型")
        
    except Exception as e:
        print(f"❌ 获取模型配置失败: {e}")


def show_primary_models():
    """显示主要模型"""
    service = ModelConfigService()
    
    try:
        print("\n当前主要模型配置:")
        print("-" * 80)
        
        # 审查模型
        review_model = service.get_primary_model('REVIEW')
        if review_model:
            print(f"代码审查模型: {review_model['model_name']}")
            print(f"  - API地址: {review_model['api_url']}")
            print(f"  - 最大tokens: {review_model['max_tokens']}")
            print(f"  - 成功率: {review_model['success_count']/(review_model['success_count']+review_model['error_count'])*100:.2f}%" 
                  if (review_model['success_count']+review_model['error_count']) > 0 else "  - 成功率: N/A")
        else:
            print("代码审查模型: 未配置")
        
        print()
        
        # 格式化模型
        format_model = service.get_primary_model('FORMAT')
        if format_model:
            print(f"格式化模型: {format_model['model_name']}")
            print(f"  - API地址: {format_model['api_url']}")
            print(f"  - 最大tokens: {format_model['max_tokens']}")
            print(f"  - 成功率: {format_model['success_count']/(format_model['success_count']+format_model['error_count'])*100:.2f}%" 
                  if (format_model['success_count']+format_model['error_count']) > 0 else "  - 成功率: N/A")
        else:
            print("格式化模型: 未配置")
        
        print("-" * 80)
        
    except Exception as e:
        print(f"❌ 获取主要模型失败: {e}")


def show_model_stats(model_id):
    """显示模型统计"""
    service = ModelConfigService()
    
    try:
        stats = service.get_model_stats(model_id)
        
        if not stats:
            print(f"❌ 未找到ID为 {model_id} 的模型")
            return
        
        print(f"\n模型统计信息 (ID: {model_id}):")
        print("-" * 50)
        print(f"模型名称: {stats['model_name']}")
        print(f"模型类型: {stats['model_type']}")
        print(f"状态: {stats['status']}")
        print(f"成功次数: {stats['success_count']}")
        print(f"失败次数: {stats['error_count']}")
        print(f"总调用次数: {stats['total_calls']}")
        print(f"成功率: {stats['success_rate']:.2f}%")
        print(f"最后使用时间: {stats['last_used_at'] or 'N/A'}")
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ 获取模型统计失败: {e}")


def refresh_config():
    """刷新配置缓存"""
    try:
        print("正在刷新模型配置缓存...")
        
        service = ModelConfigService()
        service.refresh_model_cache()
        
        print("✅ 模型配置缓存刷新成功")
        print("所有LLM服务将在下次调用时使用最新配置")
        
    except Exception as e:
        print(f"❌ 刷新配置失败: {e}")


def test_connection():
    """测试数据库连接"""
    try:
        print("正在测试数据库连接...")
        
        dao = LLMModelConfigDAO()
        review_models = dao.get_active_models_by_type(dao.ModelType.REVIEW)
        format_models = dao.get_active_models_by_type(dao.ModelType.FORMAT)
        
        print("✅ 数据库连接正常")
        print(f"找到 {len(review_models)} 个审查模型")
        print(f"找到 {len(format_models)} 个格式化模型")
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("请检查数据库配置和表结构")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='LLM模型配置管理工具')
    parser.add_argument('command', choices=['list', 'primary', 'stats', 'refresh', 'test'], 
                       help='操作命令')
    parser.add_argument('--type', choices=['review', 'format'], 
                       help='模型类型（用于list命令）')
    parser.add_argument('--id', type=int, 
                       help='模型ID（用于stats命令）')
    
    args = parser.parse_args()
    
    print("LLM模型配置管理工具")
    print("=" * 50)
    
    if args.command == 'list':
        list_models(args.type)
    elif args.command == 'primary':
        show_primary_models()
    elif args.command == 'stats':
        if args.id is None:
            print("❌ 请使用 --id 参数指定模型ID")
            return
        show_model_stats(args.id)
    elif args.command == 'refresh':
        refresh_config()
    elif args.command == 'test':
        test_connection()
    
    print("\n使用说明:")
    print("  python model_config_manager.py list              # 列出所有模型")
    print("  python model_config_manager.py list --type review # 列出审查模型")
    print("  python model_config_manager.py primary           # 显示主要模型")
    print("  python model_config_manager.py stats --id 1      # 显示模型统计")
    print("  python model_config_manager.py refresh           # 刷新配置缓存")
    print("  python model_config_manager.py test              # 测试数据库连接")


if __name__ == "__main__":
    main()
