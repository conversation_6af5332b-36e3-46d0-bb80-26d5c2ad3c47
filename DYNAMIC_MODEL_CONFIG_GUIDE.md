# 动态模型配置功能使用指南

## 功能概述

动态模型配置功能允许您将LLM模型配置存储在数据库中，实现以下能力：

- **无需重新部署**：切换模型只需更新数据库配置
- **故障转移**：支持多个备用模型，自动故障转移
- **实时监控**：记录模型使用统计和成功率
- **配置缓存**：提高性能，减少数据库查询

## 架构设计

### 数据库层
- `llm_model_config` 表：存储模型配置信息
- 支持模型类型：REVIEW（代码审查）、FORMAT（格式化）
- 支持状态管理：ACTIVE、INACTIVE、MAINTENANCE

### 服务层
- `LLMModelConfigDAO`：数据访问层
- `DynamicModelConfig`：动态配置管理
- `ModelConfigService`：配置管理服务
- `LLMService`：增强的LLM服务，支持故障转移

## 安装部署

### 1. 创建数据库表

```bash
# 执行SQL脚本创建表结构和初始数据
mysql -u your_username -p your_database < resource/sql/llm_model_config.sql
```

### 2. 验证安装

```bash
# 运行测试验证功能
python test/test_dynamic_model_config.py
```

### 3. 使用管理工具

```bash
# 查看所有模型配置
python tools/model_config_manager.py list

# 查看当前主要模型
python tools/model_config_manager.py primary

# 测试数据库连接
python tools/model_config_manager.py test
```

## 使用方法

### 代码中使用（无需修改现有代码）

```python
# 原有代码继续工作，自动使用数据库配置
from service.code_review import review_mr_changes

result = review_mr_changes("project_id", "mr_iid")
```

### 配置管理

#### 查看模型配置

```python
from common.services.model_config_service import ModelConfigService

service = ModelConfigService()

# 获取所有模型
models = service.get_all_models()

# 获取审查模型
review_models = service.get_models_by_type('REVIEW')

# 获取主要模型
primary_model = service.get_primary_model('REVIEW')
```

#### 刷新配置缓存

```python
from common.factory.service_factory import refresh_llm_service

# 刷新LLM服务配置
refresh_llm_service()
```

### 数据库操作

#### 添加新模型

```sql
INSERT INTO llm_model_config (
    model_name, model_type, api_url, api_key, 
    status, priority, max_tokens, description
) VALUES (
    'new-model-name', 'REVIEW', 'http://api.example.com/v1', 
    'your-api-key', 'INACTIVE', 3, 20000, '新的审查模型'
);
```

#### 切换主要模型

```sql
-- 将当前主要模型设为备用
UPDATE llm_model_config 
SET priority = 2 
WHERE model_type = 'REVIEW' AND priority = 1;

-- 将新模型设为主要
UPDATE llm_model_config 
SET priority = 1, status = 'ACTIVE' 
WHERE id = your_new_model_id;
```

#### 禁用模型

```sql
UPDATE llm_model_config 
SET status = 'INACTIVE' 
WHERE id = model_id;
```

## 故障转移机制

### 自动故障转移

当主要模型调用失败时，系统会：

1. **重试机制**：根据配置的重试次数进行重试
2. **指数退避**：重试间隔逐渐增加
3. **故障转移**：尝试使用备用模型（按优先级顺序）
4. **统计记录**：记录成功/失败次数

### 配置备用模型

```sql
-- 确保有多个ACTIVE状态的模型，优先级不同
INSERT INTO llm_model_config (
    model_name, model_type, api_url, api_key,
    status, priority, max_tokens, description
) VALUES 
('backup-model-1', 'REVIEW', 'http://backup1.com/v1', 'key1', 'ACTIVE', 2, 20000, '备用模型1'),
('backup-model-2', 'REVIEW', 'http://backup2.com/v1', 'key2', 'ACTIVE', 3, 20000, '备用模型2');
```

## 监控和统计

### 查看模型统计

```bash
# 使用管理工具查看统计
python tools/model_config_manager.py stats --id 1
```

### 数据库查询统计

```sql
-- 查看模型使用统计
SELECT 
    model_name,
    model_type,
    success_count,
    error_count,
    (success_count / (success_count + error_count) * 100) as success_rate,
    last_used_at
FROM llm_model_config 
WHERE status = 'ACTIVE'
ORDER BY model_type, priority;
```

## 性能优化

### 缓存机制

- **配置缓存**：模型配置缓存5分钟，减少数据库查询
- **客户端缓存**：LLM客户端实例缓存，避免重复创建
- **自动刷新**：配置变更时自动清除缓存

### 最佳实践

1. **合理设置优先级**：确保备用模型优先级递增
2. **监控成功率**：定期检查模型成功率，及时调整
3. **适当的重试次数**：避免过多重试影响性能
4. **定期清理统计**：避免统计数据过多影响查询性能

## 故障排除

### 常见问题

1. **配置不生效**
   ```bash
   # 刷新配置缓存
   python tools/model_config_manager.py refresh
   ```

2. **数据库连接失败**
   ```bash
   # 测试数据库连接
   python tools/model_config_manager.py test
   ```

3. **模型调用失败**
   - 检查API地址和密钥是否正确
   - 确认模型状态为ACTIVE
   - 查看错误日志了解具体原因

### 日志查看

```bash
# 查看相关日志
tail -f logs/llm_service.log
tail -f logs/llm_model_config_dao.log
tail -f logs/model_config_service.log
```

## 升级和维护

### 配置备份

```bash
# 备份模型配置
mysqldump -u username -p database_name llm_model_config > model_config_backup.sql
```

### 版本升级

1. 备份现有配置
2. 执行新版本的SQL脚本
3. 验证功能正常
4. 刷新配置缓存

## API参考

### ModelConfigService

- `get_all_models()` - 获取所有模型配置
- `get_models_by_type(type)` - 按类型获取模型
- `get_primary_model(type)` - 获取主要模型
- `get_model_stats(id)` - 获取模型统计
- `refresh_model_cache()` - 刷新缓存

### CodeReviewConfig

- `get_review_model_config()` - 获取审查模型配置
- `get_format_model_config()` - 获取格式化模型配置
- `clear_model_cache()` - 清除配置缓存

这个动态模型配置功能为您的AI代码审查系统提供了强大的配置管理能力，让您能够灵活地管理和切换不同的LLM模型，确保系统的高可用性和可维护性。
