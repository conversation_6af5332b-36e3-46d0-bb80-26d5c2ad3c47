# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 14:30
# <AUTHOR> 和森
# @File   : code_review_dao.py
# @Project: ai-service
# @Desc   : 代码审查数据访问层

import json
from datetime import datetime
from typing import Optional, Dict, Any
from common.utils.single_db import get_dbhandler
from common.utils.logger_util import get_logger, log_exception, log_function_call, log_execution_time

logger = get_logger("code_review_dao")


class CodeReviewDAO:
    """代码审查数据访问对象"""
    
    def __init__(self):
        """初始化DAO"""
        self.logger = logger
        
    def update_review_result(self, review_id: int, formatted_result: Dict[str, Any]) -> bool:
        """
        更新代码审查结果到数据库
        
        Args:
            review_id: 审查记录ID
            formatted_result: 格式化后的审查结果
            
        Returns:
            bool: 更新是否成功
        """
        start_time = datetime.now()
        log_function_call("update_review_result", args=(review_id, f"result_size:{len(str(formatted_result))}"))
        
        try:
            self.logger.info(f"开始更新代码审查结果到数据库 - ID: {review_id}")
            
            # 获取数据库连接
            db = get_dbhandler()
            
            # 将格式化结果转换为JSON字符串
            params_json = json.dumps(formatted_result, ensure_ascii=False)
            
            # 更新数据库中对应id的params字段
            update_sql = "UPDATE code_review_ai_info SET params = %s WHERE id = %s"
            db._exec(update_sql, params=(params_json, review_id), is_query=False)
            
            self.logger.info(f"代码审查结果已成功保存到数据库 - ID: {review_id}")
            self.logger.debug(f"保存的数据长度: {len(params_json)}")
            
            end_time = datetime.now()
            log_execution_time("update_review_result", start_time, end_time)
            
            return True
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("update_review_result", start_time, end_time)
            self.logger.error(f"更新代码审查结果失败 - ID: {review_id}, 错误: {str(e)}")
            log_exception(e, f"数据库操作异常 - ID: {review_id}")
            return False
    
    def get_review_by_id(self, review_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取代码审查记录
        
        Args:
            review_id: 审查记录ID
            
        Returns:
            Optional[Dict[str, Any]]: 审查记录，如果不存在则返回None
        """
        start_time = datetime.now()
        log_function_call("get_review_by_id", args=(review_id,))
        
        try:
            self.logger.info(f"开始查询代码审查记录 - ID: {review_id}")
            
            db = get_dbhandler()
            query_sql = "SELECT * FROM code_review_ai_info WHERE id = %s"
            result = db._exec(query_sql, params=(review_id,), is_query=True)
            
            if result:
                self.logger.info(f"成功查询到代码审查记录 - ID: {review_id}")
                # 假设返回的是元组列表，需要转换为字典
                # 这里需要根据实际的表结构调整
                return {"id": result[0][0], "params": result[0][1]} if result[0] else None
            else:
                self.logger.warning(f"未找到代码审查记录 - ID: {review_id}")
                return None
                
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("get_review_by_id", start_time, end_time)
            self.logger.error(f"查询代码审查记录失败 - ID: {review_id}")
            log_exception(e, f"数据库查询异常 - ID: {review_id}")
            return None
        finally:
            end_time = datetime.now()
            log_execution_time("get_review_by_id", start_time, end_time)
    
    def create_review_record(self, project_id: str, mr_iid: str, status: str = "processing") -> Optional[int]:
        """
        创建代码审查记录
        
        Args:
            project_id: 项目ID
            mr_iid: MR ID
            status: 状态
            
        Returns:
            Optional[int]: 创建的记录ID，失败返回None
        """
        start_time = datetime.now()
        log_function_call("create_review_record", args=(project_id, mr_iid, status))
        
        try:
            self.logger.info(f"开始创建代码审查记录 - 项目ID: {project_id}, MR ID: {mr_iid}")
            
            db = get_dbhandler()
            insert_sql = """
                INSERT INTO code_review_ai_info (project_id, mr_iid, status, created_at) 
                VALUES (%s, %s, %s, %s)
            """
            record_id = db._exec(
                insert_sql, 
                params=(project_id, mr_iid, status, datetime.now()), 
                is_query=False
            )
            
            self.logger.info(f"成功创建代码审查记录 - ID: {record_id}")
            
            end_time = datetime.now()
            log_execution_time("create_review_record", start_time, end_time)
            
            return record_id
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("create_review_record", start_time, end_time)
            self.logger.error(f"创建代码审查记录失败 - 项目ID: {project_id}, MR ID: {mr_iid}")
            log_exception(e, f"数据库插入异常 - 项目ID: {project_id}, MR ID: {mr_iid}")
            return None
