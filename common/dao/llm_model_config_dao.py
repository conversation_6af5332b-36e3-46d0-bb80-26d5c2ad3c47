# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 16:00
# <AUTHOR> 和森
# @File   : llm_model_config_dao.py
# @Project: ai-service
# @Desc   : LLM模型配置数据访问层

import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from enum import Enum
from common.utils.single_db import get_dbhandler
from common.utils.logger_util import get_logger, log_exception, log_function_call, log_execution_time


class ModelType(Enum):
    """模型类型枚举"""
    REVIEW = "REVIEW"
    FORMAT = "FORMAT"


class ModelStatus(Enum):
    """模型状态枚举"""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    MAINTENANCE = "MAINTENANCE"


class LLMModelConfig:
    """LLM模型配置数据模型"""
    
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.model_name = kwargs.get('model_name')
        self.model_type = kwargs.get('model_type')
        self.api_url = kwargs.get('api_url')
        self.api_key = kwargs.get('api_key')
        self.status = kwargs.get('status', ModelStatus.ACTIVE.value)
        self.priority = kwargs.get('priority', 1)
        self.max_tokens = kwargs.get('max_tokens')
        self.timeout_seconds = kwargs.get('timeout_seconds', 60)
        self.retry_count = kwargs.get('retry_count', 3)
        self.description = kwargs.get('description')
        self.config_json = kwargs.get('config_json')
        self.created_by = kwargs.get('created_by', 'system')
        self.created_at = kwargs.get('created_at')
        self.updated_by = kwargs.get('updated_by', 'system')
        self.updated_at = kwargs.get('updated_at')
        self.last_used_at = kwargs.get('last_used_at')
        self.error_count = kwargs.get('error_count', 0)
        self.success_count = kwargs.get('success_count', 0)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'model_name': self.model_name,
            'model_type': self.model_type,
            'api_url': self.api_url,
            'api_key': self.api_key,
            'status': self.status,
            'priority': self.priority,
            'max_tokens': self.max_tokens,
            'timeout_seconds': self.timeout_seconds,
            'retry_count': self.retry_count,
            'description': self.description,
            'config_json': self.config_json,
            'created_by': self.created_by,
            'created_at': self.created_at,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at,
            'last_used_at': self.last_used_at,
            'error_count': self.error_count,
            'success_count': self.success_count
        }


class LLMModelConfigDAO:
    """LLM模型配置数据访问对象"""
    
    def __init__(self):
        """初始化DAO"""
        self.logger = get_logger("llm_model_config_dao")
        
    def get_active_models_by_type(self, model_type: ModelType) -> List[LLMModelConfig]:
        """
        根据模型类型获取可用的模型配置列表（按优先级排序）
        
        Args:
            model_type: 模型类型
            
        Returns:
            List[LLMModelConfig]: 模型配置列表
        """
        start_time = datetime.now()
        log_function_call("get_active_models_by_type", args=(model_type.value,))
        
        try:
            self.logger.info(f"查询可用模型配置 - 类型: {model_type.value}")
            
            db = get_dbhandler()
            query_sql = """
                SELECT * FROM llm_model_config 
                WHERE model_type = %s AND status = 'ACTIVE'
                ORDER BY priority ASC
            """
            
            results = db._exec(query_sql, params=(model_type.value,), is_query=True)
            
            models = []
            if results:
                for row in results:
                    # 假设查询结果按字段顺序返回
                    model_data = {
                        'id': row[0],
                        'model_name': row[1],
                        'model_type': row[2],
                        'api_url': row[3],
                        'api_key': row[4],
                        'status': row[5],
                        'priority': row[6],
                        'max_tokens': row[7],
                        'timeout_seconds': row[8],
                        'retry_count': row[9],
                        'description': row[10],
                        'config_json': row[11],
                        'created_by': row[12],
                        'created_at': row[13],
                        'updated_by': row[14],
                        'updated_at': row[15],
                        'last_used_at': row[16],
                        'error_count': row[17],
                        'success_count': row[18]
                    }
                    models.append(LLMModelConfig(**model_data))
            
            self.logger.info(f"查询到 {len(models)} 个可用模型配置")
            
            end_time = datetime.now()
            log_execution_time("get_active_models_by_type", start_time, end_time)
            
            return models
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("get_active_models_by_type", start_time, end_time)
            self.logger.error(f"查询模型配置失败 - 类型: {model_type.value}")
            log_exception(e, f"查询模型配置时发生异常 - 类型: {model_type.value}")
            return []
    
    def get_primary_model(self, model_type: ModelType) -> Optional[LLMModelConfig]:
        """
        获取指定类型的主要模型（优先级最高的可用模型）
        
        Args:
            model_type: 模型类型
            
        Returns:
            Optional[LLMModelConfig]: 主要模型配置，如果没有可用模型则返回None
        """
        models = self.get_active_models_by_type(model_type)
        return models[0] if models else None
    
    def update_model_stats(self, model_id: int, is_success: bool, error_message: str = None) -> bool:
        """
        更新模型使用统计
        
        Args:
            model_id: 模型ID
            is_success: 是否成功
            error_message: 错误信息（可选）
            
        Returns:
            bool: 更新是否成功
        """
        start_time = datetime.now()
        log_function_call("update_model_stats", args=(model_id, is_success))
        
        try:
            self.logger.info(f"更新模型统计 - ID: {model_id}, 成功: {is_success}")
            
            db = get_dbhandler()
            
            # 调用存储过程更新统计
            call_sql = "CALL UpdateModelStats(%s, %s, %s)"
            db._exec(call_sql, params=(model_id, is_success, error_message), is_query=False)
            
            self.logger.info(f"模型统计更新成功 - ID: {model_id}")
            
            end_time = datetime.now()
            log_execution_time("update_model_stats", start_time, end_time)
            
            return True
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("update_model_stats", start_time, end_time)
            self.logger.error(f"更新模型统计失败 - ID: {model_id}")
            log_exception(e, f"更新模型统计时发生异常 - ID: {model_id}")
            return False
    
    def get_model_by_id(self, model_id: int) -> Optional[LLMModelConfig]:
        """
        根据ID获取模型配置
        
        Args:
            model_id: 模型ID
            
        Returns:
            Optional[LLMModelConfig]: 模型配置，如果不存在则返回None
        """
        start_time = datetime.now()
        log_function_call("get_model_by_id", args=(model_id,))
        
        try:
            self.logger.info(f"查询模型配置 - ID: {model_id}")
            
            db = get_dbhandler()
            query_sql = "SELECT * FROM llm_model_config WHERE id = %s"
            results = db._exec(query_sql, params=(model_id,), is_query=True)
            
            if results and results[0]:
                row = results[0]
                model_data = {
                    'id': row[0],
                    'model_name': row[1],
                    'model_type': row[2],
                    'api_url': row[3],
                    'api_key': row[4],
                    'status': row[5],
                    'priority': row[6],
                    'max_tokens': row[7],
                    'timeout_seconds': row[8],
                    'retry_count': row[9],
                    'description': row[10],
                    'config_json': row[11],
                    'created_by': row[12],
                    'created_at': row[13],
                    'updated_by': row[14],
                    'updated_at': row[15],
                    'last_used_at': row[16],
                    'error_count': row[17],
                    'success_count': row[18]
                }
                
                self.logger.info(f"查询到模型配置 - ID: {model_id}")
                
                end_time = datetime.now()
                log_execution_time("get_model_by_id", start_time, end_time)
                
                return LLMModelConfig(**model_data)
            else:
                self.logger.warning(f"未找到模型配置 - ID: {model_id}")
                return None
                
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("get_model_by_id", start_time, end_time)
            self.logger.error(f"查询模型配置失败 - ID: {model_id}")
            log_exception(e, f"查询模型配置时发生异常 - ID: {model_id}")
            return None
