# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 14:30
# <AUTHOR> 和森
# @File   : code_review_models.py
# @Project: ai-service
# @Desc   : 代码审查业务模型

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime


@dataclass
class FileChange:
    """文件变更模型"""
    new_path: str
    diff: str
    added_lines: int = 0
    
    def __post_init__(self):
        """计算新增行数"""
        if not self.added_lines:
            self.added_lines = self.diff.count("\n+")


@dataclass
class ReviewIssue:
    """审查问题模型"""
    description: str
    file: str
    lines: List[Dict[str, int]]  # [{"start": 10, "end": 15}, ...]
    impact: str
    suggestion: str
    example: str
    priority: str
    
    def __post_init__(self):
        """验证优先级"""
        from common.config.code_review_config import CodeReviewConfig
        if not CodeReviewConfig.validate_priority(self.priority):
            raise ValueError(f"无效的优先级: {self.priority}")


@dataclass
class ReviewScore:
    """审查评分模型"""
    code_logic: int = 0
    code_style: int = 0
    performance: int = 0
    security: int = 0
    readability: int = 0
    
    @property
    def total_score(self) -> int:
        """计算总分"""
        return self.code_logic + self.code_style + self.performance + self.security + self.readability
    
    def to_dict(self) -> Dict[str, int]:
        """转换为字典"""
        return {
            "code_logic": self.code_logic,
            "code_style": self.code_style,
            "performance": self.performance,
            "security": self.security,
            "readability": self.readability,
            "total": self.total_score
        }


@dataclass
class CodeReviewRequest:
    """代码审查请求模型"""
    project_id: str
    mr_iid: str
    changes: List[FileChange] = field(default_factory=list)
    max_tokens: int = 20000
    max_code_plus: int = 5000
    
    @property
    def total_added_lines(self) -> int:
        """计算总新增行数"""
        return sum(change.added_lines for change in self.changes)
    
    def validate(self) -> bool:
        """验证请求是否有效"""
        if not self.project_id or not self.mr_iid:
            return False
        if not self.changes:
            return False
        if self.total_added_lines > self.max_code_plus:
            return False
        return True


@dataclass
class CodeReviewResult:
    """代码审查结果模型"""
    project_id: str
    mr_iid: str
    issues: List[ReviewIssue] = field(default_factory=list)
    score: Optional[ReviewScore] = None
    summary: str = ""
    raw_response: str = ""
    formatted_response: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    
    @property
    def issue_count(self) -> int:
        """获取问题数量"""
        return len(self.issues)
    
    @property
    def high_priority_issues(self) -> List[ReviewIssue]:
        """获取高优先级问题"""
        return [issue for issue in self.issues if issue.priority == "高"]
    
    @property
    def medium_priority_issues(self) -> List[ReviewIssue]:
        """获取中优先级问题"""
        return [issue for issue in self.issues if issue.priority == "中"]
    
    @property
    def low_priority_issues(self) -> List[ReviewIssue]:
        """获取低优先级问题"""
        return [issue for issue in self.issues if issue.priority == "低"]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "project_id": self.project_id,
            "mr_iid": self.mr_iid,
            "issues": [
                {
                    "description": issue.description,
                    "file": issue.file,
                    "lines": issue.lines,
                    "impact": issue.impact,
                    "suggestion": issue.suggestion,
                    "example": issue.example,
                    "priority": issue.priority
                }
                for issue in self.issues
            ],
            "score": self.score.to_dict() if self.score else None,
            "summary": self.summary,
            "issue_count": self.issue_count,
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def from_llm_response(cls, project_id: str, mr_iid: str, raw_response: str, 
                         formatted_response: Dict[str, Any]) -> 'CodeReviewResult':
        """从LLM响应创建结果对象"""
        issues = []
        for issue_data in formatted_response.get("issues", []):
            try:
                issue = ReviewIssue(
                    description=issue_data.get("description", ""),
                    file=issue_data.get("file", ""),
                    lines=issue_data.get("lines", []),
                    impact=issue_data.get("impact", ""),
                    suggestion=issue_data.get("suggestion", ""),
                    example=issue_data.get("example", ""),
                    priority=issue_data.get("priority", "中")
                )
                issues.append(issue)
            except ValueError as e:
                # 跳过无效的问题数据
                continue
        
        return cls(
            project_id=project_id,
            mr_iid=mr_iid,
            issues=issues,
            raw_response=raw_response,
            formatted_response=formatted_response
        )
