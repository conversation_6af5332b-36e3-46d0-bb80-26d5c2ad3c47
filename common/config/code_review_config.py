# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 14:30
# <AUTHOR> 和森
# @File   : code_review_config.py
# @Project: ai-service
# @Desc   : 代码审查相关配置，支持动态模型配置

import time
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from .config import REVIEW_MAX_TOKENS, REVIEW_MAX_CODE_PLUS, LLM_API_KEY, LLM_API_URL


class DynamicModelConfig:
    """动态模型配置类，支持从数据库读取配置"""

    def __init__(self):
        """初始化动态配置"""
        self._cache = {}
        self._cache_expire_time = {}
        self._cache_duration = 300  # 缓存5分钟
        self._dao = None

    @property
    def dao(self):
        """延迟加载DAO，避免循环导入"""
        if self._dao is None:
            from common.dao.llm_model_config_dao import LLMModelConfigDAO
            self._dao = LLMModelConfigDAO()
        return self._dao

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache_expire_time:
            return False
        return datetime.now() < self._cache_expire_time[cache_key]

    def _set_cache(self, cache_key: str, value: Any):
        """设置缓存"""
        self._cache[cache_key] = value
        self._cache_expire_time[cache_key] = datetime.now() + timedelta(seconds=self._cache_duration)

    def get_review_model_config(self) -> Optional[Dict[str, Any]]:
        """获取代码审查模型配置"""
        cache_key = "review_model"

        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        try:
            from common.dao.llm_model_config_dao import ModelType
            model = self.dao.get_primary_model(ModelType.REVIEW)

            if model:
                config = {
                    'id': model.id,
                    'model_name': model.model_name,
                    'api_url': model.api_url,
                    'api_key': model.api_key,
                    'max_tokens': model.max_tokens or REVIEW_MAX_TOKENS,
                    'timeout_seconds': model.timeout_seconds,
                    'retry_count': model.retry_count
                }
                self._set_cache(cache_key, config)
                return config
            else:
                # 如果数据库中没有配置，使用默认配置
                from .config import LLM_MODEL
                default_config = {
                    'id': None,
                    'model_name': LLM_MODEL,
                    'api_url': LLM_API_URL,
                    'api_key': LLM_API_KEY,
                    'max_tokens': REVIEW_MAX_TOKENS,
                    'timeout_seconds': 60,
                    'retry_count': 3
                }
                self._set_cache(cache_key, default_config)
                return default_config

        except Exception as e:
            # 发生异常时使用默认配置
            from common.utils.logger_util import get_logger
            logger = get_logger("dynamic_config")
            logger.error(f"获取审查模型配置失败，使用默认配置: {e}")

            from .config import LLM_MODEL
            default_config = {
                'id': None,
                'model_name': LLM_MODEL,
                'api_url': LLM_API_URL,
                'api_key': LLM_API_KEY,
                'max_tokens': REVIEW_MAX_TOKENS,
                'timeout_seconds': 60,
                'retry_count': 3
            }
            return default_config

    def get_format_model_config(self) -> Optional[Dict[str, Any]]:
        """获取格式化模型配置"""
        cache_key = "format_model"

        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        try:
            from common.dao.llm_model_config_dao import ModelType
            model = self.dao.get_primary_model(ModelType.FORMAT)

            if model:
                config = {
                    'id': model.id,
                    'model_name': model.model_name,
                    'api_url': model.api_url,
                    'api_key': model.api_key,
                    'max_tokens': model.max_tokens or 10000,
                    'timeout_seconds': model.timeout_seconds,
                    'retry_count': model.retry_count
                }
                self._set_cache(cache_key, config)
                return config
            else:
                # 如果数据库中没有配置，使用默认配置
                from .config import LLM_FORMAT_MODLE
                default_config = {
                    'id': None,
                    'model_name': LLM_FORMAT_MODLE,
                    'api_url': LLM_API_URL,
                    'api_key': LLM_API_KEY,
                    'max_tokens': 10000,
                    'timeout_seconds': 60,
                    'retry_count': 3
                }
                self._set_cache(cache_key, default_config)
                return default_config

        except Exception as e:
            # 发生异常时使用默认配置
            from common.utils.logger_util import get_logger
            logger = get_logger("dynamic_config")
            logger.error(f"获取格式化模型配置失败，使用默认配置: {e}")

            from .config import LLM_FORMAT_MODLE
            default_config = {
                'id': None,
                'model_name': LLM_FORMAT_MODLE,
                'api_url': LLM_API_URL,
                'api_key': LLM_API_KEY,
                'max_tokens': 10000,
                'timeout_seconds': 60,
                'retry_count': 3
            }
            return default_config

    def clear_cache(self):
        """清除缓存"""
        self._cache.clear()
        self._cache_expire_time.clear()


class CodeReviewConfig:
    """代码审查配置类，支持动态模型配置"""

    # 支持的文件类型
    SUPPORTED_FILE_TYPES: List[str] = [
        "*.go",      # Go 语言文件
        "*.java",    # Java 语言文件
        "*.py",      # Python 语言文件
        "*.js",      # JavaScript 语言文件
        "*.ts",      # TypeScript 语言文件
        "*.kt",      # Kotlin 语言文件
        "*.swift",   # Swift 语言文件
        "*.cs",      # C# 语言文件
        "*.rs",      # Rust 语言文件
        "*.cpp",     # C++ 语言文件
        "*.c",       # C 语言文件
        "*.h",       # C 语言头文件
        "*.hpp",     # C++ 头文件
        "*.ino",     # Arduino 语言文件
        "*.rb",      # Ruby 语言文件
        "*.php",     # PHP 语言文件
        "*.lua",     # Lua 语言文件
        "*.pl",      # Perl 语言文件
        "*.r",       # R 语言文件
        "*.dart",    # Dart 语言文件
        "*.scala",   # Scala 语言文件
        "*.groovy",  # Groovy 语言文件
    ]

    # 审查限制配置
    MAX_TOKENS: int = REVIEW_MAX_TOKENS
    MAX_CODE_PLUS: int = REVIEW_MAX_CODE_PLUS

    # 审查评分配置
    SCORING_WEIGHTS = {
        "code_logic": 40,      # 代码逻辑权重
        "code_style": 20,      # 代码风格权重
        "performance": 20,     # 性能优化权重
        "security": 10,        # 安全性权重
        "readability": 10,     # 可读性权重
    }

    # 优先级配置
    PRIORITY_LEVELS = ["高", "中", "低"]

    # 动态模型配置实例
    _dynamic_config = None
    
    @classmethod
    def get_dynamic_config(cls) -> DynamicModelConfig:
        """获取动态配置实例（单例）"""
        if cls._dynamic_config is None:
            cls._dynamic_config = DynamicModelConfig()
        return cls._dynamic_config

    @classmethod
    def get_review_model_config(cls) -> Dict[str, Any]:
        """获取代码审查模型配置"""
        return cls.get_dynamic_config().get_review_model_config()

    @classmethod
    def get_format_model_config(cls) -> Dict[str, Any]:
        """获取格式化模型配置"""
        return cls.get_dynamic_config().get_format_model_config()

    @classmethod
    def clear_model_cache(cls):
        """清除模型配置缓存"""
        if cls._dynamic_config:
            cls._dynamic_config.clear_cache()

    @classmethod
    def get_supported_file_types(cls) -> List[str]:
        """获取支持的文件类型列表"""
        return cls.SUPPORTED_FILE_TYPES.copy()

    @classmethod
    def is_supported_file_type(cls, file_path: str) -> bool:
        """检查文件是否为支持的类型"""
        import fnmatch
        return any(fnmatch.fnmatch(file_path, pattern) for pattern in cls.SUPPORTED_FILE_TYPES)

    @classmethod
    def get_max_tokens(cls) -> int:
        """获取最大token限制（从动态配置中获取）"""
        review_config = cls.get_review_model_config()
        return review_config.get('max_tokens', cls.MAX_TOKENS)

    @classmethod
    def get_max_code_plus(cls) -> int:
        """获取最大代码新增行数限制"""
        return cls.MAX_CODE_PLUS

    @classmethod
    def get_scoring_weights(cls) -> dict:
        """获取评分权重配置"""
        return cls.SCORING_WEIGHTS.copy()

    @classmethod
    def get_priority_levels(cls) -> List[str]:
        """获取优先级级别列表"""
        return cls.PRIORITY_LEVELS.copy()

    @classmethod
    def validate_priority(cls, priority: str) -> bool:
        """验证优先级是否有效"""
        return priority in cls.PRIORITY_LEVELS
