# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 14:30
# <AUTHOR> 和森
# @File   : code_review_config.py
# @Project: ai-service
# @Desc   : 代码审查相关配置

from typing import List
from .config import REVIEW_MAX_TOKENS, REVIEW_MAX_CODE_PLUS


class CodeReviewConfig:
    """代码审查配置类"""
    
    # 支持的文件类型
    SUPPORTED_FILE_TYPES: List[str] = [
        "*.go",      # Go 语言文件
        "*.java",    # Java 语言文件
        "*.py",      # Python 语言文件
        "*.js",      # JavaScript 语言文件
        "*.ts",      # TypeScript 语言文件
        "*.kt",      # Kotlin 语言文件
        "*.swift",   # Swift 语言文件
        "*.cs",      # C# 语言文件
        "*.rs",      # Rust 语言文件
        "*.cpp",     # C++ 语言文件
        "*.c",       # C 语言文件
        "*.h",       # C 语言头文件
        "*.hpp",     # C++ 头文件
        "*.ino",     # Arduino 语言文件
        "*.rb",      # Ruby 语言文件
        "*.php",     # PHP 语言文件
        "*.lua",     # Lua 语言文件
        "*.pl",      # Perl 语言文件
        "*.r",       # R 语言文件
        "*.dart",    # Dart 语言文件
        "*.scala",   # Scala 语言文件
        "*.groovy",  # Groovy 语言文件
    ]
    
    # 审查限制配置
    MAX_TOKENS: int = REVIEW_MAX_TOKENS
    MAX_CODE_PLUS: int = REVIEW_MAX_CODE_PLUS
    
    # 审查评分配置
    SCORING_WEIGHTS = {
        "code_logic": 40,      # 代码逻辑权重
        "code_style": 20,      # 代码风格权重
        "performance": 20,     # 性能优化权重
        "security": 10,        # 安全性权重
        "readability": 10,     # 可读性权重
    }
    
    # 优先级配置
    PRIORITY_LEVELS = ["高", "中", "低"]
    
    @classmethod
    def get_supported_file_types(cls) -> List[str]:
        """获取支持的文件类型列表"""
        return cls.SUPPORTED_FILE_TYPES.copy()
    
    @classmethod
    def is_supported_file_type(cls, file_path: str) -> bool:
        """检查文件是否为支持的类型"""
        import fnmatch
        return any(fnmatch.fnmatch(file_path, pattern) for pattern in cls.SUPPORTED_FILE_TYPES)
    
    @classmethod
    def get_max_tokens(cls) -> int:
        """获取最大token限制"""
        return cls.MAX_TOKENS
    
    @classmethod
    def get_max_code_plus(cls) -> int:
        """获取最大代码新增行数限制"""
        return cls.MAX_CODE_PLUS
    
    @classmethod
    def get_scoring_weights(cls) -> dict:
        """获取评分权重配置"""
        return cls.SCORING_WEIGHTS.copy()
    
    @classmethod
    def get_priority_levels(cls) -> List[str]:
        """获取优先级级别列表"""
        return cls.PRIORITY_LEVELS.copy()
    
    @classmethod
    def validate_priority(cls, priority: str) -> bool:
        """验证优先级是否有效"""
        return priority in cls.PRIORITY_LEVELS
