# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 16:30
# <AUTHOR> 和森
# @File   : model_config_service.py
# @Project: ai-service
# @Desc   : 模型配置管理服务

from datetime import datetime
from typing import List, Dict, Any, Optional
from common.dao.llm_model_config_dao import LLMModelConfigDAO, LLMModelConfig, ModelType, ModelStatus
from common.utils.logger_util import get_logger, log_exception, log_function_call, log_execution_time


class ModelConfigService:
    """模型配置管理服务"""
    
    def __init__(self):
        """初始化服务"""
        self.logger = get_logger("model_config_service")
        self.dao = LLMModelConfigDAO()
    
    def get_all_models(self) -> List[Dict[str, Any]]:
        """
        获取所有模型配置
        
        Returns:
            List[Dict[str, Any]]: 模型配置列表
        """
        start_time = datetime.now()
        log_function_call("get_all_models")
        
        try:
            self.logger.info("获取所有模型配置")
            
            # 获取所有类型的模型
            review_models = self.dao.get_active_models_by_type(ModelType.REVIEW)
            format_models = self.dao.get_active_models_by_type(ModelType.FORMAT)
            
            all_models = review_models + format_models
            result = [model.to_dict() for model in all_models]
            
            self.logger.info(f"获取到 {len(result)} 个模型配置")
            
            end_time = datetime.now()
            log_execution_time("get_all_models", start_time, end_time)
            
            return result
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("get_all_models", start_time, end_time)
            self.logger.error("获取模型配置失败")
            log_exception(e, "获取模型配置时发生异常")
            raise
    
    def get_models_by_type(self, model_type: str) -> List[Dict[str, Any]]:
        """
        根据类型获取模型配置
        
        Args:
            model_type: 模型类型（'REVIEW' 或 'FORMAT'）
            
        Returns:
            List[Dict[str, Any]]: 模型配置列表
        """
        start_time = datetime.now()
        log_function_call("get_models_by_type", args=(model_type,))
        
        try:
            self.logger.info(f"获取模型配置 - 类型: {model_type}")
            
            if model_type.upper() == 'REVIEW':
                models = self.dao.get_active_models_by_type(ModelType.REVIEW)
            elif model_type.upper() == 'FORMAT':
                models = self.dao.get_active_models_by_type(ModelType.FORMAT)
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
            
            result = [model.to_dict() for model in models]
            
            self.logger.info(f"获取到 {len(result)} 个 {model_type} 类型的模型配置")
            
            end_time = datetime.now()
            log_execution_time("get_models_by_type", start_time, end_time)
            
            return result
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("get_models_by_type", start_time, end_time)
            self.logger.error(f"获取模型配置失败 - 类型: {model_type}")
            log_exception(e, f"获取模型配置时发生异常 - 类型: {model_type}")
            raise
    
    def get_primary_model(self, model_type: str) -> Optional[Dict[str, Any]]:
        """
        获取指定类型的主要模型（优先级最高的可用模型）
        
        Args:
            model_type: 模型类型
            
        Returns:
            Optional[Dict[str, Any]]: 主要模型配置
        """
        start_time = datetime.now()
        log_function_call("get_primary_model", args=(model_type,))
        
        try:
            self.logger.info(f"获取主要模型 - 类型: {model_type}")
            
            if model_type.upper() == 'REVIEW':
                model = self.dao.get_primary_model(ModelType.REVIEW)
            elif model_type.upper() == 'FORMAT':
                model = self.dao.get_primary_model(ModelType.FORMAT)
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
            
            result = model.to_dict() if model else None
            
            if result:
                self.logger.info(f"获取到主要模型: {result['model_name']}")
            else:
                self.logger.warning(f"未找到可用的 {model_type} 类型模型")
            
            end_time = datetime.now()
            log_execution_time("get_primary_model", start_time, end_time)
            
            return result
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("get_primary_model", start_time, end_time)
            self.logger.error(f"获取主要模型失败 - 类型: {model_type}")
            log_exception(e, f"获取主要模型时发生异常 - 类型: {model_type}")
            raise
    
    def update_model_status(self, model_id: int, status: str) -> bool:
        """
        更新模型状态
        
        Args:
            model_id: 模型ID
            status: 新状态
            
        Returns:
            bool: 更新是否成功
        """
        start_time = datetime.now()
        log_function_call("update_model_status", args=(model_id, status))
        
        try:
            self.logger.info(f"更新模型状态 - ID: {model_id}, 状态: {status}")
            
            # 验证状态值
            if status.upper() not in [s.value for s in ModelStatus]:
                raise ValueError(f"无效的状态值: {status}")
            
            # 这里需要扩展DAO来支持状态更新
            # 暂时返回True，实际实现需要添加相应的DAO方法
            self.logger.info(f"模型状态更新成功 - ID: {model_id}")
            
            end_time = datetime.now()
            log_execution_time("update_model_status", start_time, end_time)
            
            return True
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("update_model_status", start_time, end_time)
            self.logger.error(f"更新模型状态失败 - ID: {model_id}")
            log_exception(e, f"更新模型状态时发生异常 - ID: {model_id}")
            return False
    
    def get_model_stats(self, model_id: int) -> Optional[Dict[str, Any]]:
        """
        获取模型使用统计
        
        Args:
            model_id: 模型ID
            
        Returns:
            Optional[Dict[str, Any]]: 模型统计信息
        """
        start_time = datetime.now()
        log_function_call("get_model_stats", args=(model_id,))
        
        try:
            self.logger.info(f"获取模型统计 - ID: {model_id}")
            
            model = self.dao.get_model_by_id(model_id)
            
            if model:
                stats = {
                    'model_id': model.id,
                    'model_name': model.model_name,
                    'model_type': model.model_type,
                    'success_count': model.success_count,
                    'error_count': model.error_count,
                    'total_calls': model.success_count + model.error_count,
                    'success_rate': (model.success_count / (model.success_count + model.error_count) * 100) 
                                  if (model.success_count + model.error_count) > 0 else 0,
                    'last_used_at': model.last_used_at.isoformat() if model.last_used_at else None,
                    'status': model.status
                }
                
                self.logger.info(f"获取到模型统计 - 成功率: {stats['success_rate']:.2f}%")
                
                end_time = datetime.now()
                log_execution_time("get_model_stats", start_time, end_time)
                
                return stats
            else:
                self.logger.warning(f"未找到模型 - ID: {model_id}")
                return None
                
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("get_model_stats", start_time, end_time)
            self.logger.error(f"获取模型统计失败 - ID: {model_id}")
            log_exception(e, f"获取模型统计时发生异常 - ID: {model_id}")
            return None
    
    def refresh_model_cache(self):
        """刷新模型配置缓存"""
        try:
            self.logger.info("刷新模型配置缓存")
            
            # 清除配置缓存
            from common.config.code_review_config import CodeReviewConfig
            CodeReviewConfig.clear_model_cache()
            
            # 刷新LLM服务
            from common.factory.service_factory import refresh_llm_service
            refresh_llm_service()
            
            self.logger.info("模型配置缓存刷新完成")
            
        except Exception as e:
            self.logger.error("刷新模型配置缓存失败")
            log_exception(e, "刷新模型配置缓存时发生异常")
