# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 14:30
# <AUTHOR> 和森
# @File   : llm_service.py
# @Project: ai-service
# @Desc   : LLM服务抽象层

import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from llama_index.llms.openai import OpenAI
from llama_index.core.llms import ChatMessage
from llama_index.core.prompts import PromptTemplate

from common.config.code_review_config import CodeReviewConfig
from common.basic.prompts import CODE_REVIEW_PROMPT, FORMAT_PROMPT_TEMPLATE, example_response
from common.utils.logger_util import get_logger, log_exception, log_function_call, log_execution_time


class LLMService:
    """LLM服务类，封装大模型相关操作，支持动态配置和故障转移"""

    def __init__(self):
        """初始化LLM服务"""
        self.logger = get_logger("llm_service")
        self._review_client = None
        self._format_client = None
        self._current_review_config = None
        self._current_format_config = None
        self._dao = None

    @property
    def dao(self):
        """延迟加载DAO，避免循环导入"""
        if self._dao is None:
            from common.dao.llm_model_config_dao import LLMModelConfigDAO
            self._dao = LLMModelConfigDAO()
        return self._dao

    def _create_client(self, config: Dict[str, Any], is_format: bool = False) -> OpenAI:
        """创建LLM客户端，支持超时配置"""
        additional_kwargs = {}
        if is_format:
            additional_kwargs["response_format"] = {"type": "json_object"}

        # 设置超时时间
        timeout_seconds = config.get('timeout_seconds', 60)

        client = OpenAI(
            api_key=config['api_key'],
            api_base=config['api_url'],
            default_headers={"model": config['model_name']},
            additional_kwargs=additional_kwargs,
            timeout=timeout_seconds  # 设置超时时间
        )

        self.logger.info(f"创建LLM客户端 - 模型: {config['model_name']}, 类型: {'格式化' if is_format else '审查'}, 超时: {timeout_seconds}秒")
        return client

    def get_review_client(self) -> OpenAI:
        """获取代码审查客户端，支持动态配置"""
        current_config = CodeReviewConfig.get_review_model_config()

        # 如果配置发生变化，重新创建客户端
        if (self._review_client is None or
            self._current_review_config != current_config):

            self._review_client = self._create_client(current_config, is_format=False)
            self._current_review_config = current_config

        return self._review_client

    def get_format_client(self) -> OpenAI:
        """获取格式化客户端，支持动态配置"""
        current_config = CodeReviewConfig.get_format_model_config()

        # 如果配置发生变化，重新创建客户端
        if (self._format_client is None or
            self._current_format_config != current_config):

            self._format_client = self._create_client(current_config, is_format=True)
            self._current_format_config = current_config

        return self._format_client
    
    def _call_llm_with_retry(self, client: OpenAI, messages: List[ChatMessage],
                           config: Dict[str, Any], model_type: str) -> str:
        """
        调用LLM并支持重试和故障转移

        Args:
            client: LLM客户端
            messages: 消息列表
            config: 模型配置
            model_type: 模型类型（'review' 或 'format'）

        Returns:
            str: LLM响应
        """
        retry_count = config.get('retry_count', 3)
        timeout_seconds = config.get('timeout_seconds', 60)
        model_id = config.get('id')

        for attempt in range(retry_count):
            try:
                self.logger.info(f"调用LLM - 模型: {config['model_name']}, 尝试: {attempt + 1}/{retry_count}, 超时: {timeout_seconds}秒")

                start_time = time.time()

                # 使用超时控制调用
                try:
                    # 注意：LlamaIndex的OpenAI客户端超时应该在创建时设置
                    # 这里我们添加额外的时间检查作为双重保险
                    response = client.chat(messages=messages)
                    duration = time.time() - start_time

                    # 检查是否超时（双重保险）
                    if duration > timeout_seconds:
                        raise TimeoutError(f"LLM调用超时: {duration:.3f}秒 > {timeout_seconds}秒")

                    self.logger.info(f"LLM调用成功 - 耗时: {duration:.3f}秒")

                    # 更新成功统计
                    if model_id:
                        self.dao.update_model_stats(model_id, True)

                    return str(response)

                except TimeoutError as timeout_error:
                    raise timeout_error
                except Exception as call_error:
                    # 检查是否是超时相关的异常
                    error_msg = str(call_error).lower()
                    if 'timeout' in error_msg or 'timed out' in error_msg:
                        raise TimeoutError(f"LLM调用超时: {call_error}")
                    else:
                        raise call_error

            except Exception as e:
                self.logger.warning(f"LLM调用失败 - 尝试: {attempt + 1}/{retry_count}, 错误: {str(e)}")

                # 更新失败统计
                if model_id:
                    self.dao.update_model_stats(model_id, False, str(e))

                if attempt == retry_count - 1:
                    # 最后一次重试失败，尝试故障转移
                    return self._try_failover(messages, model_type)

                # 等待后重试
                time.sleep(2 ** attempt)  # 指数退避

        raise Exception(f"LLM调用失败，已重试{retry_count}次")

    def _try_failover(self, messages: List[ChatMessage], model_type: str) -> str:
        """
        尝试故障转移到备用模型

        Args:
            messages: 消息列表
            model_type: 模型类型

        Returns:
            str: LLM响应
        """
        try:
            from common.dao.llm_model_config_dao import ModelType

            # 获取备用模型列表
            if model_type == 'review':
                models = self.dao.get_active_models_by_type(ModelType.REVIEW)
            else:
                models = self.dao.get_active_models_by_type(ModelType.FORMAT)

            # 跳过当前失败的模型，尝试下一个
            for model in models[1:]:  # 跳过第一个（当前失败的）
                try:
                    self.logger.info(f"尝试故障转移到备用模型: {model.model_name}")

                    # 创建备用客户端
                    backup_config = {
                        'id': model.id,
                        'model_name': model.model_name,
                        'api_url': model.api_url,
                        'api_key': model.api_key,
                        'timeout_seconds': model.timeout_seconds,
                        'retry_count': 1  # 备用模型只重试1次
                    }

                    backup_client = self._create_client(backup_config, is_format=(model_type == 'format'))
                    response = backup_client.chat(messages=messages)

                    self.logger.info(f"故障转移成功 - 备用模型: {model.model_name}")

                    # 更新成功统计
                    self.dao.update_model_stats(model.id, True)

                    return str(response)

                except Exception as backup_error:
                    self.logger.warning(f"备用模型调用失败: {model.model_name}, 错误: {str(backup_error)}")
                    self.dao.update_model_stats(model.id, False, str(backup_error))
                    continue

            raise Exception("所有备用模型都不可用")

        except Exception as e:
            self.logger.error(f"故障转移失败: {str(e)}")
            raise

    def review_code_changes(self, changes_str: str) -> str:
        """
        使用LLM进行代码审查，支持故障转移

        Args:
            changes_str: 格式化后的代码变更内容

        Returns:
            str: LLM生成的审查意见
        """
        start_time = datetime.now()
        log_function_call("review_code_changes", args=(f"changes_length:{len(changes_str)}",))

        try:
            self.logger.info(f"开始LLM代码审查 - 内容长度: {len(changes_str)}")

            # 创建审查prompt
            prompt_template = PromptTemplate(CODE_REVIEW_PROMPT)
            review_prompt = prompt_template.format(changes=changes_str)

            self.logger.debug(f"审查prompt长度: {len(review_prompt)}")

            # 获取当前配置和客户端
            config = CodeReviewConfig.get_review_model_config()
            client = self.get_review_client()

            # 调用LLM（支持重试和故障转移）
            messages = [ChatMessage(role="user", content=review_prompt)]
            response = self._call_llm_with_retry(client, messages, config, 'review')

            end_time = datetime.now()
            log_execution_time("review_code_changes", start_time, end_time)

            return response

        except Exception as e:
            end_time = datetime.now()
            log_execution_time("review_code_changes", start_time, end_time)
            self.logger.error("LLM代码审查异常")
            log_exception(e, "LLM代码审查过程中发生异常")
            raise
    
    def format_review_response(self, llm_output: str) -> Dict[str, Any]:
        """
        格式化LLM输出为结构化数据，支持故障转移

        Args:
            llm_output: LLM原始输出

        Returns:
            Dict[str, Any]: 结构化的审查结果
        """
        start_time = datetime.now()
        log_function_call("format_review_response", args=(f"output_length:{len(llm_output)}",))

        try:
            self.logger.info(f"开始格式化LLM输出 - 内容长度: {len(llm_output)}")

            # 创建格式化prompt
            prompt_template = PromptTemplate(FORMAT_PROMPT_TEMPLATE)
            json_prompt = prompt_template.format(text=llm_output, example_response=example_response)

            self.logger.debug(f"格式化prompt长度: {len(json_prompt)}")

            # 获取当前配置和客户端
            config = CodeReviewConfig.get_format_model_config()
            client = self.get_format_client()

            # 调用格式化模型（支持重试和故障转移）
            messages = [ChatMessage(role="user", content=json_prompt)]
            response = self._call_llm_with_retry(client, messages, config, 'format')

            # 解析JSON响应
            try:
                # 从响应中提取内容
                if hasattr(response, 'message') and hasattr(response.message, 'content'):
                    content = response.message.content
                else:
                    content = str(response)

                data = json.loads(content)
                issues_count = len(data.get('issues', []))
                self.logger.info(f"JSON解析成功 - 提取到问题数: {issues_count}")
            except json.JSONDecodeError as json_error:
                self.logger.error(f"JSON解析失败: {json_error}")
                self.logger.debug(f"原始响应内容: {response}")
                # 返回空的结构化数据作为fallback
                data = {"issues": []}
                self.logger.warning("返回空的结构化数据作为fallback")

            end_time = datetime.now()
            log_execution_time("format_review_response", start_time, end_time)

            return data

        except Exception as e:
            end_time = datetime.now()
            log_execution_time("format_review_response", start_time, end_time)
            self.logger.error("LLM输出格式化异常")
            log_exception(e, "格式化LLM输出过程中发生异常")
            raise

    def refresh_clients(self):
        """刷新客户端配置（清除缓存的客户端）"""
        self.logger.info("刷新LLM客户端配置")
        self._review_client = None
        self._format_client = None
        self._current_review_config = None
        self._current_format_config = None

        # 清除配置缓存
        CodeReviewConfig.clear_model_cache()
