# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 14:30
# <AUTHOR> 和森
# @File   : llm_service.py
# @Project: ai-service
# @Desc   : LLM服务抽象层

import json
from datetime import datetime
from typing import Dict, Any, Optional
from llama_index.llms.openai import OpenAI
from llama_index.core.llms import ChatMessage
from llama_index.core.prompts import PromptTemplate

from common.config.config import LLM_MODEL, LLM_API_KEY, LLM_API_URL, LLM_FORMAT_MODLE
from common.basic.prompts import CODE_REVIEW_PROMPT, FORMAT_PROMPT_TEMPLATE, example_response
from common.utils.logger_util import get_logger, log_exception, log_function_call, log_execution_time


class LLMService:
    """LLM服务类，封装大模型相关操作"""
    
    def __init__(self):
        """初始化LLM服务"""
        self.logger = get_logger("llm_service")
        self._review_client = None
        self._format_client = None
        
    @property
    def review_client(self) -> OpenAI:
        """获取代码审查客户端（单例模式）"""
        if self._review_client is None:
            self._review_client = OpenAI(
                api_key=LLM_API_KEY,
                api_base=LLM_API_URL,
                default_headers={"model": LLM_MODEL}
            )
            self.logger.info(f"初始化代码审查LLM客户端 - 模型: {LLM_MODEL}")
        return self._review_client
    
    @property
    def format_client(self) -> OpenAI:
        """获取格式化客户端（单例模式）"""
        if self._format_client is None:
            self._format_client = OpenAI(
                api_key=LLM_API_KEY,
                api_base=LLM_API_URL,
                default_headers={"model": LLM_FORMAT_MODLE},
                additional_kwargs={
                    "response_format": {"type": "json_object"}
                }
            )
            self.logger.info(f"初始化格式化LLM客户端 - 模型: {LLM_FORMAT_MODLE}")
        return self._format_client
    
    def review_code_changes(self, changes_str: str) -> str:
        """
        使用LLM进行代码审查
        
        Args:
            changes_str: 格式化后的代码变更内容
            
        Returns:
            str: LLM生成的审查意见
        """
        start_time = datetime.now()
        log_function_call("review_code_changes", args=(f"changes_length:{len(changes_str)}",))
        
        try:
            self.logger.info(f"开始LLM代码审查 - 内容长度: {len(changes_str)}")
            
            # 创建审查prompt
            prompt_template = PromptTemplate(CODE_REVIEW_PROMPT)
            review_prompt = prompt_template.format(changes=changes_str)
            
            self.logger.debug(f"审查prompt长度: {len(review_prompt)}")
            
            # 调用LLM
            llm_start_time = datetime.now()
            messages = [ChatMessage(role="user", content=review_prompt)]
            response = self.review_client.chat(messages=messages)
            
            llm_end_time = datetime.now()
            llm_duration = (llm_end_time - llm_start_time).total_seconds()
            self.logger.info(f"LLM代码审查完成，耗时: {llm_duration:.3f}秒")
            
            end_time = datetime.now()
            log_execution_time("review_code_changes", start_time, end_time)
            
            return str(response)
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("review_code_changes", start_time, end_time)
            self.logger.error("LLM代码审查异常")
            log_exception(e, "LLM代码审查过程中发生异常")
            raise
    
    def format_review_response(self, llm_output: str) -> Dict[str, Any]:
        """
        格式化LLM输出为结构化数据
        
        Args:
            llm_output: LLM原始输出
            
        Returns:
            Dict[str, Any]: 结构化的审查结果
        """
        start_time = datetime.now()
        log_function_call("format_review_response", args=(f"output_length:{len(llm_output)}",))
        
        try:
            self.logger.info(f"开始格式化LLM输出 - 内容长度: {len(llm_output)}")
            
            # 创建格式化prompt
            prompt_template = PromptTemplate(FORMAT_PROMPT_TEMPLATE)
            json_prompt = prompt_template.format(text=llm_output, example_response=example_response)
            
            self.logger.debug(f"格式化prompt长度: {len(json_prompt)}")
            
            # 调用格式化模型
            llm_start_time = datetime.now()
            messages = [ChatMessage(role="user", content=json_prompt)]
            response = self.format_client.chat(messages=messages)
            
            llm_end_time = datetime.now()
            llm_duration = (llm_end_time - llm_start_time).total_seconds()
            self.logger.info(f"LLM格式化完成，耗时: {llm_duration:.3f}秒")
            
            # 解析JSON响应
            try:
                data = json.loads(response.message.content)
                issues_count = len(data.get('issues', []))
                self.logger.info(f"JSON解析成功 - 提取到问题数: {issues_count}")
            except json.JSONDecodeError as json_error:
                self.logger.error(f"JSON解析失败: {json_error}")
                self.logger.debug(f"原始响应内容: {response.message.content}")
                # 返回空的结构化数据作为fallback
                data = {"issues": []}
                self.logger.warning("返回空的结构化数据作为fallback")
            
            end_time = datetime.now()
            log_execution_time("format_review_response", start_time, end_time)
            
            return data
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("format_review_response", start_time, end_time)
            self.logger.error("LLM输出格式化异常")
            log_exception(e, "格式化LLM输出过程中发生异常")
            raise
