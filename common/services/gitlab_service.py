# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 14:30
# <AUTHOR> 和森
# @File   : gitlab_service.py
# @Project: ai-service
# @Desc   : GitLab服务抽象层

import fnmatch
from datetime import datetime
from typing import List, Dict, Any
from common.thirdparty.gitlabapi import GitLabAPI
from common.utils.git_diff_format import annotate_added_line_numbers
from common.utils.logger_util import get_logger, log_exception, log_function_call, log_execution_time
from common.basic.exception import MRFileChangeException, MRFetchException


class GitLabService:
    """GitLab服务类，封装GitLab相关操作"""
    
    def __init__(self, supported_file_types: List[str] = None, max_code_plus: int = 5000):
        """
        初始化GitLab服务
        
        Args:
            supported_file_types: 支持的文件类型列表
            max_code_plus: 最大代码新增行数限制
        """
        self.logger = get_logger("gitlab_service")
        self.gitlab_api = GitLabAPI()
        self.supported_file_types = supported_file_types or []
        self.max_code_plus = max_code_plus
        
    def filter_files_by_type(self, file_list: List[str]) -> List[str]:
        """
        根据文件类型过滤文件列表
        
        Args:
            file_list: 文件列表
            
        Returns:
            List[str]: 过滤后的文件列表
        """
        start_time = datetime.now()
        log_function_call("filter_files_by_type", args=(f"file_count:{len(file_list)}",))
        
        try:
            self.logger.info(f"开始过滤文件列表，总文件数: {len(file_list)}")
            
            filtered_files = []
            for file in file_list:
                if any(fnmatch.fnmatch(file, pattern) for pattern in self.supported_file_types):
                    filtered_files.append(file)
                    self.logger.debug(f"文件 {file} 匹配指定类型，保留")
                else:
                    self.logger.debug(f"文件 {file} 不匹配指定类型，跳过")
            
            self.logger.info(f"文件过滤完成，保留文件数: {len(filtered_files)}")
            
            end_time = datetime.now()
            log_execution_time("filter_files_by_type", start_time, end_time)
            
            return filtered_files
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("filter_files_by_type", start_time, end_time)
            self.logger.error("文件类型过滤异常")
            log_exception(e, "过滤文件类型时发生异常")
            raise
    
    def get_mr_changes(self, project_id: str, iid: str) -> List[Dict[str, Any]]:
        """
        获取MR变更信息并进行过滤处理
        
        Args:
            project_id: GitLab项目ID
            iid: Merge Request ID
            
        Returns:
            List[Dict[str, Any]]: 过滤后的变更文件列表
        """
        start_time = datetime.now()
        log_function_call("get_mr_changes", args=(project_id, iid))
        
        try:
            self.logger.info(f"开始获取MR变更信息 - 项目ID: {project_id}, MR ID: {iid}")
            
            # 调用GitLab API获取变更
            res = self.gitlab_api.get_mr_changes(project_id, iid)
            changes = res.get("changes", [])
            
            self.logger.info(f"获取到原始变更文件数: {len(changes)}")
            
            if not changes:
                self.logger.warning(f"MR中没有文件变更 - 项目ID: {project_id}, MR ID: {iid}")
                raise MRFileChangeException("MR 中没有文件变更")
            
            # 处理变更文件
            filtered_changes = []
            total_diff_plus = 0
            
            for i, change in enumerate(changes, 1):
                new_path = change.get("new_path", "")
                diff = change.get("diff", "")
                
                self.logger.debug(f"处理第{i}个文件: {new_path}")
                
                # 注释行号
                diff = annotate_added_line_numbers(diff)
                
                # 检查是否为支持的文件类型
                if not any(fnmatch.fnmatch(new_path, pattern) for pattern in self.supported_file_types):
                    self.logger.debug(f"文件 {new_path} 不在支持类型范围内，跳过")
                    continue
                
                # 计算新增行数
                diff_plus = diff.count("\n+")
                total_diff_plus += diff_plus
                filtered_changes.append({"new_path": new_path, "diff": diff})
                
                self.logger.info(f"文件 {new_path} 符合条件，新增行数: {diff_plus}")
            
            self.logger.info(f"文件过滤完成，符合条件的文件数: {len(filtered_changes)}, 总新增行数: {total_diff_plus}")
            
            # 检查是否有符合条件的变更
            if not filtered_changes:
                self.logger.warning("MR中没有需要处理的代码变更(所有变更文件都不在支持类型范围内)")
                raise MRFileChangeException("MR 中没有需要处理的代码变更(所有变更文件都不在支持类型范围内)")
            
            # 检查新增行数是否超过限制
            if total_diff_plus > self.max_code_plus:
                self.logger.warning(f"新增变更行数({total_diff_plus})超过限制({self.max_code_plus})，不进行AI代码评审")
                raise MRFileChangeException(f"MR 中需要处理的代码新增变更行数超过{self.max_code_plus}，不进行AI代码评审.")
            
            end_time = datetime.now()
            log_execution_time("get_mr_changes", start_time, end_time)
            
            return filtered_changes
            
        except MRFileChangeException:
            end_time = datetime.now()
            log_execution_time("get_mr_changes", start_time, end_time)
            raise
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("get_mr_changes", start_time, end_time)
            self.logger.error(f"获取MR变更异常 - 项目ID: {project_id}, MR ID: {iid}")
            log_exception(e, f"获取MR变更时发生错误 - 项目ID: {project_id}, MR ID: {iid}")
            raise MRFetchException(f"获取 MR 变更时发生错误: {str(e)}")
    
    def format_changes_for_review(self, changes: List[Dict[str, Any]]) -> str:
        """
        将变更内容格式化为适合审查的字符串
        
        Args:
            changes: 变更文件列表
            
        Returns:
            str: 格式化后的变更内容
        """
        start_time = datetime.now()
        log_function_call("format_changes_for_review", args=(f"changes_count:{len(changes)}",))
        
        try:
            self.logger.info(f"开始格式化变更内容，文件数: {len(changes)}")
            
            changes_str = "\n\n".join([
                f"### 文件: {change['new_path']}\n```diff\n{change['diff']}\n```" 
                for change in changes
            ])
            
            self.logger.info(f"变更内容格式化完成，总长度: {len(changes_str)}")
            
            end_time = datetime.now()
            log_execution_time("format_changes_for_review", start_time, end_time)
            
            return changes_str
            
        except Exception as e:
            end_time = datetime.now()
            log_execution_time("format_changes_for_review", start_time, end_time)
            self.logger.error("格式化变更内容异常")
            log_exception(e, "格式化变更内容时发生异常")
            raise
