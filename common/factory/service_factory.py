# -*- coding: utf-8 -*-
# @Time   : 2025/08/15 14:30
# <AUTHOR> 和森
# @File   : service_factory.py
# @Project: ai-service
# @Desc   : 服务工厂类，管理服务实例的创建和生命周期

from typing import Optional
from common.services.llm_service import LLMService
from common.services.gitlab_service import GitLabService
from common.dao.code_review_dao import CodeReviewDAO
from common.dao.llm_model_config_dao import LLMModelConfigDAO
from common.config.code_review_config import CodeReviewConfig
from common.utils.logger_util import get_logger


class ServiceFactory:
    """服务工厂类，使用单例模式管理服务实例，支持动态配置"""

    _instance: Optional['ServiceFactory'] = None
    _llm_service: Optional[LLMService] = None
    _gitlab_service: Optional[GitLabService] = None
    _code_review_dao: Optional[CodeReviewDAO] = None
    _llm_model_config_dao: Optional[LLMModelConfigDAO] = None

    def __new__(cls) -> 'ServiceFactory':
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(ServiceFactory, cls).__new__(cls)
            cls._instance.logger = get_logger("service_factory")
            cls._instance.logger.info("初始化服务工厂（支持动态配置）")
        return cls._instance
    
    def get_llm_service(self) -> LLMService:
        """获取LLM服务实例（单例）"""
        if self._llm_service is None:
            self.logger.info("创建LLM服务实例")
            self._llm_service = LLMService()
        return self._llm_service
    
    def get_gitlab_service(self) -> GitLabService:
        """获取GitLab服务实例（单例）"""
        if self._gitlab_service is None:
            self.logger.info("创建GitLab服务实例")
            self._gitlab_service = GitLabService(
                supported_file_types=CodeReviewConfig.get_supported_file_types(),
                max_code_plus=CodeReviewConfig.get_max_code_plus()
            )
        return self._gitlab_service
    
    def get_code_review_dao(self) -> CodeReviewDAO:
        """获取代码审查DAO实例（单例）"""
        if self._code_review_dao is None:
            self.logger.info("创建代码审查DAO实例")
            self._code_review_dao = CodeReviewDAO()
        return self._code_review_dao

    def get_llm_model_config_dao(self) -> LLMModelConfigDAO:
        """获取LLM模型配置DAO实例（单例）"""
        if self._llm_model_config_dao is None:
            self.logger.info("创建LLM模型配置DAO实例")
            self._llm_model_config_dao = LLMModelConfigDAO()
        return self._llm_model_config_dao

    def refresh_llm_service(self):
        """刷新LLM服务（重新加载配置）"""
        self.logger.info("刷新LLM服务配置")
        if self._llm_service:
            self._llm_service.refresh_clients()

        # 清除配置缓存
        CodeReviewConfig.clear_model_cache()

    def reset_services(self):
        """重置所有服务实例（主要用于测试）"""
        self.logger.info("重置所有服务实例")
        self._llm_service = None
        self._gitlab_service = None
        self._code_review_dao = None
        self._llm_model_config_dao = None
    
    @classmethod
    def get_instance(cls) -> 'ServiceFactory':
        """获取工厂实例"""
        return cls()


# 提供便捷的全局访问函数
def get_service_factory() -> ServiceFactory:
    """获取服务工厂实例"""
    return ServiceFactory.get_instance()


def get_llm_service() -> LLMService:
    """获取LLM服务实例"""
    return get_service_factory().get_llm_service()


def get_gitlab_service() -> GitLabService:
    """获取GitLab服务实例"""
    return get_service_factory().get_gitlab_service()


def get_code_review_dao() -> CodeReviewDAO:
    """获取代码审查DAO实例"""
    return get_service_factory().get_code_review_dao()


def get_llm_model_config_dao() -> LLMModelConfigDAO:
    """获取LLM模型配置DAO实例"""
    return get_service_factory().get_llm_model_config_dao()


def refresh_llm_service():
    """刷新LLM服务配置"""
    return get_service_factory().refresh_llm_service()
