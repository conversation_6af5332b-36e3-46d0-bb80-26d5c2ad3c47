import sys
sys.path.append('/Users/<USER>/ai-service')
import re
from datetime import datetime
from common.thirdparty.gitlabapi import GitLabAPI
from common.utils.logger_util import get_logger, log_function_call, log_execution_time

# 获取日志器
logger = get_logger("git_diff_format")

def annotate_added_line_numbers(diff_text: str) -> str:
    """
    为git diff中的新增行添加行号注释

    Args:
        diff_text: git diff文本

    Returns:
        str: 添加了行号注释的diff文本
    """
    start_time = datetime.now()
    # log_function_call("annotate_added_line_numbers", args=(f"diff_length={len(diff_text)}",))

    try:
        logger.debug(f"开始为diff添加行号注释，diff长度: {len(diff_text)}")

        lines = diff_text.splitlines(keepends=True)
        annotated_lines = []

        current_new_lineno = None
        line_offset = 0
        added_lines_count = 0

        for i, line in enumerate(lines):
            if line.startswith('@@'):
                match = re.search(r'\+(\d+)(?:,(\d+))?', line)
                if match:
                    current_new_lineno = int(match.group(1))
                    line_offset = 0
                    logger.debug(f"找到hunk头，新行号起始: {current_new_lineno}")
                annotated_lines.append(line)
                continue

            # 新增行（+开头，非文件标识）
            if line.startswith('+') and not line.startswith('+++'):
                lineno = current_new_lineno + line_offset
                newline = '\n'
                if line.endswith('\r\n'):
                    newline = '\r\n'
                elif line.endswith('\r'):
                    newline = '\r'
                content = line.rstrip('\r\n')
                annotated_lines.append(f"{content}  # line {lineno}{newline}")
                line_offset += 1
                added_lines_count += 1
                logger.debug(f"添加行号注释: 第{lineno}行")

            # 删除行（-开头，非文件标识）
            elif line.startswith('-') and not line.startswith('---'):
                annotated_lines.append(line)

            # 上下文行（空格开头或空行） → 消耗新行号
            else:
                annotated_lines.append(line)
                line_offset += 1

        result = ''.join(annotated_lines)

        end_time = datetime.now()
        # log_execution_time("annotate_added_line_numbers", start_time, end_time)

        logger.debug(f"diff行号注释完成，处理了{added_lines_count}个新增行")
        return result

    except Exception as e:
        end_time = datetime.now()
        # log_execution_time("annotate_added_line_numbers", start_time, end_time)
        logger.error(f"diff行号注释异常: {str(e)}")
        raise



if __name__ == "__main__":
    # 从提供的changes数据中提取diff字符串
    res = GitLabAPI().get_mr_changes(4142, 306)
    changes = res.get("changes", [])
    for change in changes:
        diff = change.get("diff", "")
        # print(f"===raw diff==={diff}")
        print(f"===annotated diff===\n{annotate_added_line_numbers(diff)}")