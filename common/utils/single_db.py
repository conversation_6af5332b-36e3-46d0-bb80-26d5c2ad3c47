# -*- coding: utf-8 -*-
# @Time   : 2022/8/10 10:58
# <AUTHOR> 王志强
# @Email  : <EMAIL>
# @File   : single_db.py
# @Project: pipeline-job
# @Desc   : 主要实现了xxx功能

import os
import pymysql
from common.config.db_conf import *

# 所有dbhandler入口都是这里
def get_dbhandler(db_name=DB_NAME):
    return DBHandler(host=DB_HOST, user=DB_USERNAME, passwd=DB_PASSWD, port=DB_PORT, db_name=db_name)

class DBException(Exception):
    pass


class DBHandler:
    # portal
    def __init__(self, host=DB_HOST, user=DB_USERNAME, passwd=DB_PASSWD, port=DB_PORT, db_name=DB_NAME):
        self.host = host
        self.user = user
        self.passwd = passwd
        self.port = int(port)
        self.db_name = db_name
        self.charset = "utf8"
        self.log_url = "%sconsole" % os.getenv("BUILD_URL")
        self.conns = {}

    def get_conn(self):
        return pymysql.connect(host=self.host, user=self.user, password=self.passwd,
                               database=self.db_name, port=self.port, charset=self.charset)

    def _exec(self, command, params='', is_query=False):
        connection = None
        cursor = None
        try:
            connection = self.get_conn()
        except Exception as error:
            msg = "%s:%s:%s连接错误：%s" %(self.host, self.port, self.db_name, str(error))
            raise DBException(msg, self.log_url, self.host)

        try:
            cursor = connection.cursor()
            print(f'execute sql: {command}')
            cursor.execute(command, params) if params else cursor.execute(command)
            if is_query:
                result = cursor.fetchall()
                return result
            else:
                last_id = cursor.lastrowid
                connection.commit()
                return last_id
        except Exception as error:
            msg = "%s:%s:%s中执行SQL语句%s出错。错误信息：%s" %(self.host, self.port, self.db_name, command, str(error))
            raise DBException(msg)
        finally:
            close(cursor)
            close(connection)

    def query(self, command):
        return self._exec(command, is_query=True)

    def modify(self, command):
        self._exec(command)

    def insert(self, command, params=''):
        return self._exec(command) if not params else self._exec(command, params=params)

def close(closeableObject):
    if closeableObject:
        try:
            closeableObject.close()
        except Exception:
            pass


class PostgresDBHandler:
    def __init__(self, host, user, passwd, port, db_name):
        self.host = host
        self.user = user
        self.passwd = passwd
        self.port = port
        self.db_name = db_name
        self.charset = "utf8"
        self.conns = {}

    def get_conn(self):
        conn = psycopg2.connect(host=self.host, user=self.user, password=self.passwd,
                                database=self.db_name, port=self.port)
        return conn

    def _exec(self, command, params='', is_query=False):
        connection = None
        cursor = None
        try:
            connection = self.get_conn()
            cursor = connection.cursor()
            print(f'executing sql: {command}')
            cursor.execute(command, params) if params else cursor.execute(command)
            if is_query:
                result= cursor.fetchall()
                return result
            else:
                connection.commit()
                return cursor.rowcount  # Return the number of rows affected
        except Exception as error:
            msg = f"{self.host}:{self.port}:{self.db_name}中执行SQL语句{command}出错。错误信息：{str(error)}"
            raise DBException(msg)
        finally:
            if cursor is not None:
                cursor.close()
            if connection is not None:
                connection.close()

    def query(self, command):
        return self._exec(command, is_query=True)

    def modify(self, command):
        self._exec(command)

    def insert(self, command, params=None):
        return self._exec(command, params=params)


def _main():
    sonar_db_handler = DBHandler('*************', 'bd_snoar', '8hx#mgaX', 3306, 'bd_snoar')
    # sonar_db_handler.insert('insert into organization_members (organization_uuid, user_id) values (%s, %s)',('test',448))
    # sonar_db_handler.insert('insert into organization_members (organization_uuid, user_id) values (\'test2\', 449)')
    print(sonar_db_handler.query('select uuid,name,last_connection_date,updated_at from users where login=\'gw00269192\''))
    # print(sonar_db_handler.query('select uuid from organizations WHERE kee=\'default-organization\''))
    # db_handler = get_dbhandler()
    # print(db_handler.query('select count(1) from app'))
    # last_id = db_handler.insert("insert into admin_user (id, user_name, role_key) value (null, 'test', 'test');")
    # print(last_id)



if __name__ == "__main__":
    _main()
