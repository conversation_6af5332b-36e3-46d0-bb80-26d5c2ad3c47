-- LLM模型配置表
-- 用于存储大模型配置信息，支持动态切换模型而无需重新部署代码

CREATE TABLE IF NOT EXISTS `llm_model_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_name` varchar(255) NOT NULL COMMENT '模型名称，如：default/qwen3-235b-a22b',
  `model_type` enum('REVIEW', 'FORMAT') NOT NULL COMMENT '模型类型：REVIEW-代码审查模型，FORMAT-格式化模型',
  `api_url` varchar(500) NOT NULL COMMENT 'API地址',
  `api_key` varchar(500) NOT NULL COMMENT 'API密钥',
  `status` enum('ACTIVE', 'INACTIVE', 'MAINTENANCE') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-可用，INACTIVE-不可用，MAINTENANCE-维护中',
  `priority` int(11) NOT NULL DEFAULT 1 COMMENT '优先级，数字越小优先级越高，用于故障转移',
  `max_tokens` int(11) DEFAULT NULL COMMENT '最大token数限制',
  `timeout_seconds` int(11) DEFAULT 60 COMMENT '超时时间（秒）',
  `retry_count` int(11) DEFAULT 3 COMMENT '重试次数',
  `description` text COMMENT '模型描述',
  `config_json` json DEFAULT NULL COMMENT '额外配置参数（JSON格式）',
  `created_by` varchar(100) DEFAULT 'system' COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(100) DEFAULT 'system' COMMENT '更新人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `error_count` int(11) DEFAULT 0 COMMENT '错误次数统计',
  `success_count` int(11) DEFAULT 0 COMMENT '成功次数统计',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_type_priority` (`model_type`, `priority`),
  KEY `idx_model_type_status` (`model_type`, `status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='LLM模型配置表';

-- 插入默认配置数据
INSERT INTO `llm_model_config` (
  `model_name`, 
  `model_type`, 
  `api_url`, 
  `api_key`, 
  `status`, 
  `priority`, 
  `max_tokens`, 
  `description`
) VALUES 
-- 代码审查模型配置
(
  'default/qwen3-235b-a22b', 
  'REVIEW', 
  'http://llmproxy.gwm.cn/v1', 
  'rgSPIT/JD10l6+BO32vYjPn3pmgu7BwzZLTIfNuVLKCHWac3cIZPPUToP1XVYK4L+91coq1Xl8mu', 
  'ACTIVE', 
  1, 
  20000, 
  '主要代码审查模型'
),
-- 备用代码审查模型
(
  'default/qwen3-235b-a22b-instruct', 
  'REVIEW', 
  'http://llmproxy.gwm.cn/v1', 
  'rgSPIT/JD10l6+BO32vYjPn3pmgu7BwzZLTIfNuVLKCHWac3cIZPPUToP1XVYK4L+91coq1Xl8mu', 
  'INACTIVE', 
  2, 
  20000, 
  '备用代码审查模型'
),
-- 格式化模型配置
(
  'default/qwen3-8b', 
  'FORMAT', 
  'http://llmproxy.gwm.cn/v1', 
  'rgSPIT/JD10l6+BO32vYjPn3pmgu7BwzZLTIfNuVLKCHWac3cIZPPUToP1XVYK4L+91coq1Xl8mu', 
  'ACTIVE', 
  1, 
  10000, 
  '主要格式化模型'
),
-- 备用格式化模型
(
  'default/qwen3-32b', 
  'FORMAT', 
  'http://llmproxy.gwm.cn/v1', 
  'rgSPIT/JD10l6+BO32vYjPn3pmgu7BwzZLTIfNuVLKCHWac3cIZPPUToP1XVYK4L+91coq1Xl8mu', 
  'INACTIVE', 
  2, 
  15000, 
  '备用格式化模型'
);

-- 创建视图，方便查询可用模型
CREATE OR REPLACE VIEW `v_active_llm_models` AS
SELECT 
  `id`,
  `model_name`,
  `model_type`,
  `api_url`,
  `api_key`,
  `priority`,
  `max_tokens`,
  `timeout_seconds`,
  `retry_count`,
  `config_json`,
  `last_used_at`,
  `error_count`,
  `success_count`
FROM `llm_model_config`
WHERE `status` = 'ACTIVE'
ORDER BY `model_type`, `priority`;

-- 创建存储过程，用于更新模型使用统计
DELIMITER //
CREATE PROCEDURE `UpdateModelStats`(
  IN p_model_id INT,
  IN p_is_success BOOLEAN,
  IN p_error_message TEXT
)
BEGIN
  DECLARE EXIT HANDLER FOR SQLEXCEPTION
  BEGIN
    ROLLBACK;
    RESIGNAL;
  END;
  
  START TRANSACTION;
  
  IF p_is_success THEN
    UPDATE `llm_model_config` 
    SET 
      `success_count` = `success_count` + 1,
      `last_used_at` = NOW()
    WHERE `id` = p_model_id;
  ELSE
    UPDATE `llm_model_config` 
    SET 
      `error_count` = `error_count` + 1,
      `last_used_at` = NOW()
    WHERE `id` = p_model_id;
  END IF;
  
  COMMIT;
END //
DELIMITER ;
